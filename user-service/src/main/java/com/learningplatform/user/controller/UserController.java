package com.learningplatform.user.controller;

import com.learningplatform.common.response.Result;
import com.learningplatform.user.dto.UserInfoResponse;
import com.learningplatform.user.dto.UserUpdateRequest;
import com.learningplatform.user.entity.User;
import com.learningplatform.user.service.UserService;
import com.learningplatform.user.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/api/user")
@CrossOrigin(origins = "*")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 获取用户信息
     * @param request HTTP请求
     * @return 用户信息
     */
    @GetMapping("/profile")
    public Result<UserInfoResponse> getProfile(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            User user = userService.findById(userId);
            if (user == null) {
                return Result.error("1001", "用户不存在");
            }
            UserInfoResponse userInfo = new UserInfoResponse(user);
            return Result.success(userInfo);
        } catch (Exception e) {
            return Result.error("1004", e.getMessage());
        }
    }
    
    /**
     * 更新用户信息
     * @param updateRequest 更新请求
     * @param request HTTP请求
     * @return 更新结果
     */
    @PutMapping("/profile")
    public Result<UserInfoResponse> updateProfile(@Valid @RequestBody UserUpdateRequest updateRequest, 
                                                  HttpServletRequest request) {
        try {
            Long userId = getUserIdFromToken(request);
            User user = userService.findById(userId);
            if (user == null) {
                return Result.error("1001", "用户不存在");
            }
            
            // 更新用户信息
            if (StringUtils.hasText(updateRequest.getEmail())) {
                // 检查邮箱是否已被其他用户使用
                User existingUser = userService.findByEmail(updateRequest.getEmail());
                if (existingUser != null && !existingUser.getId().equals(userId)) {
                    return Result.error("1003", "邮箱已被其他用户使用");
                }
                user.setEmail(updateRequest.getEmail());
            }
            
            if (StringUtils.hasText(updateRequest.getNickname())) {
                user.setNickname(updateRequest.getNickname());
            }
            
            if (StringUtils.hasText(updateRequest.getAvatarUrl())) {
                user.setAvatarUrl(updateRequest.getAvatarUrl());
            }
            
            if (StringUtils.hasText(updateRequest.getPhone())) {
                user.setPhone(updateRequest.getPhone());
            }
            
            User updatedUser = userService.updateUser(user);
            UserInfoResponse userInfo = new UserInfoResponse(updatedUser);
            return Result.success(userInfo);
        } catch (Exception e) {
            return Result.error("1004", e.getMessage());
        }
    }
    
    /**
     * 根据用户ID获取用户基本信息（供其他服务调用）
     * @param userId 用户ID
     * @return 用户基本信息
     */
    @GetMapping("/basic/{userId}")
    public Result<UserInfoResponse> getUserBasicInfo(@PathVariable Long userId) {
        try {
            User user = userService.findById(userId);
            if (user == null) {
                return Result.error("1001", "用户不存在");
            }
            UserInfoResponse userInfo = new UserInfoResponse(user);
            return Result.success(userInfo);
        } catch (Exception e) {
            return Result.error("1004", e.getMessage());
        }
    }
    
    /**
     * 从请求中获取用户ID
     * @param request HTTP请求
     * @return 用户ID
     */
    private Long getUserIdFromToken(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            throw new RuntimeException("未提供有效的认证令牌");
        }
        
        String token = authHeader.substring(7);
        return jwtUtil.getUserIdFromToken(token);
    }
}