package com.learningplatform.course.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.learningplatform.course.dto.CourseCreateRequest;
import com.learningplatform.course.dto.CourseUpdateRequest;
import com.learningplatform.course.entity.Course;

public interface CourseService {
    
    /**
     * 获取已发布的课程列表
     */
    IPage<Course> getPublishedCourses(Page<Course> page);
    
    /**
     * 获取教师的课程列表
     */
    IPage<Course> getCoursesByTeacher(Page<Course> page, Long teacherId);
    
    /**
     * 根据ID获取课程详情
     */
    Course getCourseById(Long courseId);
    
    /**
     * 搜索课程
     */
    IPage<Course> searchCourses(Page<Course> page, String keyword);
    
    /**
     * 创建课程
     */
    Course createCourse(CourseCreateRequest request, Long teacherId);
    
    /**
     * 更新课程
     */
    Course updateCourse(Long courseId, CourseUpdateRequest request, Long teacherId);
    
    /**
     * 删除课程
     */
    void deleteCourse(Long courseId, Long teacherId);
    
    /**
     * 报名课程
     */
    void enrollCourse(Long courseId, Long userId);
}