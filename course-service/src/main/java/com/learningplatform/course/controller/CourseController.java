package com.learningplatform.course.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.learningplatform.common.response.Result;
import com.learningplatform.course.dto.CourseCreateRequest;
import com.learningplatform.course.dto.CourseUpdateRequest;
import com.learningplatform.course.entity.Course;
import com.learningplatform.course.service.CourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/api/course")
@CrossOrigin(origins = "*")
public class CourseController {
    
    @Autowired
    private CourseService courseService;
    
    /**
     * 获取课程列表
     */
    @GetMapping("/list")
    public Result getCourses(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "12") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String level,
            @RequestParam(required = false) String sort,
            @RequestParam(required = false) Boolean teacherOnly,
            HttpServletRequest request) {
        
        Page<Course> pageParam = new Page<>(page, size);
        
        try {
            if (teacherOnly != null && teacherOnly) {
                // 获取当前用户的课程
                String userIdHeader = request.getHeader("X-User-Id");
                if (userIdHeader == null) {
                    return Result.error("未登录");
                }
                Long teacherId = Long.parseLong(userIdHeader);
                return Result.success(courseService.getCoursesByTeacher(pageParam, teacherId));
            } else if (keyword != null && !keyword.trim().isEmpty()) {
                // 搜索课程
                return Result.success(courseService.searchCourses(pageParam, keyword.trim()));
            } else {
                // 获取已发布的课程列表
                return Result.success(courseService.getPublishedCourses(pageParam));
            }
        } catch (Exception e) {
            return Result.error("获取课程列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取课程详情
     */
    @GetMapping("/{id}")
    public Result getCourse(@PathVariable Long id) {
        try {
            Course course = courseService.getCourseById(id);
            if (course == null) {
                return Result.error("课程不存在");
            }
            return Result.success(course);
        } catch (Exception e) {
            return Result.error("获取课程详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 搜索课程
     */
    @GetMapping("/search")
    public Result searchCourses(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "12") int size) {
        
        Page<Course> pageParam = new Page<>(page, size);
        
        try {
            return Result.success(courseService.searchCourses(pageParam, keyword));
        } catch (Exception e) {
            return Result.error("搜索课程失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建课程
     */
    @PostMapping
    public Result createCourse(@RequestBody CourseCreateRequest request, HttpServletRequest httpRequest) {
        try {
            String userIdHeader = httpRequest.getHeader("X-User-Id");
            if (userIdHeader == null) {
                return Result.error("未登录");
            }
            
            Long teacherId = Long.parseLong(userIdHeader);
            Course course = courseService.createCourse(request, teacherId);
            return Result.success(course);
        } catch (Exception e) {
            return Result.error("创建课程失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新课程
     */
    @PutMapping("/{id}")
    public Result updateCourse(@PathVariable Long id, @RequestBody CourseUpdateRequest request, HttpServletRequest httpRequest) {
        try {
            String userIdHeader = httpRequest.getHeader("X-User-Id");
            if (userIdHeader == null) {
                return Result.error("未登录");
            }
            
            Long teacherId = Long.parseLong(userIdHeader);
            Course course = courseService.updateCourse(id, request, teacherId);
            return Result.success(course);
        } catch (Exception e) {
            return Result.error("更新课程失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除课程
     */
    @DeleteMapping("/{id}")
    public Result deleteCourse(@PathVariable Long id, HttpServletRequest httpRequest) {
        try {
            String userIdHeader = httpRequest.getHeader("X-User-Id");
            if (userIdHeader == null) {
                return Result.error("未登录");
            }
            
            Long teacherId = Long.parseLong(userIdHeader);
            courseService.deleteCourse(id, teacherId);
            return Result.success("删除成功");
        } catch (Exception e) {
            return Result.error("删除课程失败: " + e.getMessage());
        }
    }
    
    /**
     * 报名课程
     */
    @PostMapping("/{id}/enroll")
    public Result enrollCourse(@PathVariable Long id, HttpServletRequest request) {
        try {
            String userIdHeader = request.getHeader("X-User-Id");
            if (userIdHeader == null) {
                return Result.error("未登录");
            }
            
            Long userId = Long.parseLong(userIdHeader);
            courseService.enrollCourse(id, userId);
            return Result.success("报名成功");
        } catch (Exception e) {
            return Result.error("报名失败: " + e.getMessage());
        }
    }
}