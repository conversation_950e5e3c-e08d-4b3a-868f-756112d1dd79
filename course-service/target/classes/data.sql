-- 创建课程分类表
CREATE TABLE IF NOT EXISTS course_categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES course_categories(id)
);

-- 创建用户表（简化版，用于关联）
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('STUDENT', 'TEACHER', 'ADMIN') DEFAULT 'STUDENT',
    avatar_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- 创建课程表
CREATE TABLE IF NOT EXISTS courses (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    teacher_id BIGINT NOT NULL,
    category_id BIGINT,
    difficulty_level ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED'),
    cover_image VARCHAR(255),
    price DECIMAL(10,2) DEFAULT 0.00,
    status ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') DEFAULT 'DRAFT',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES course_categories(id)
);

-- 创建课程章节表
CREATE TABLE IF NOT EXISTS course_chapters (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    course_id BIGINT NOT NULL,
    title VARCHAR(200) NOT NULL,
    content_type ENUM('VIDEO', 'DOCUMENT', 'QUIZ'),
    content_url VARCHAR(500),
    duration_minutes INT,
    order_index INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id)
);

-- 创建课程注册表
CREATE TABLE IF NOT EXISTS course_enrollments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    UNIQUE KEY unique_enrollment (user_id, course_id),
    FOREIGN KEY (course_id) REFERENCES courses(id)
);

-- 插入测试数据
-- 插入课程分类
INSERT INTO course_categories (id, name, description) VALUES 
(1, '编程开发', '编程和软件开发相关课程'),
(2, '设计创意', '设计和创意相关课程'),
(3, '商业管理', '商业和管理相关课程'),
(4, '语言学习', '语言学习相关课程'),
(5, '职业技能', '职业技能提升相关课程');

-- 插入测试用户
INSERT INTO users (id, username, email, password_hash, role) VALUES 
(1, '张老师', '<EMAIL>', '$2a$10$encrypted_password', 'TEACHER'),
(2, '李老师', '<EMAIL>', '$2a$10$encrypted_password', 'TEACHER'),
(3, '王老师', '<EMAIL>', '$2a$10$encrypted_password', 'TEACHER'),
(4, '学生1', '<EMAIL>', '$2a$10$encrypted_password', 'STUDENT'),
(5, '学生2', '<EMAIL>', '$2a$10$encrypted_password', 'STUDENT');

-- 插入测试课程
INSERT INTO courses (id, title, description, teacher_id, category_id, difficulty_level, cover_image, price, status) VALUES 
(1, 'Java编程基础', '从零开始学习Java编程，包含基础语法、面向对象编程、集合框架等内容', 1, 1, 'BEGINNER', 'https://example.com/course1.jpg', 0.00, 'PUBLISHED'),
(2, 'Spring Boot实战', '深入学习Spring Boot框架，构建现代化的Java Web应用', 1, 1, 'INTERMEDIATE', 'https://example.com/course2.jpg', 99.00, 'PUBLISHED'),
(3, 'Vue.js前端开发', '现代前端框架Vue.js开发，从基础到高级应用', 2, 1, 'INTERMEDIATE', 'https://example.com/course3.jpg', 129.00, 'PUBLISHED'),
(4, 'UI/UX设计入门', '用户界面和用户体验设计基础课程', 3, 2, 'BEGINNER', 'https://example.com/course4.jpg', 79.00, 'PUBLISHED'),
(5, 'Python数据分析', 'Python在数据分析和机器学习中的应用', 2, 1, 'ADVANCED', 'https://example.com/course5.jpg', 199.00, 'DRAFT');

-- 插入课程章节
INSERT INTO course_chapters (course_id, title, content_type, content_url, duration_minutes, order_index) VALUES 
(1, 'Java环境搭建', 'VIDEO', 'https://example.com/video1.mp4', 30, 0),
(1, 'Java基础语法', 'VIDEO', 'https://example.com/video2.mp4', 45, 1),
(1, '面向对象编程', 'VIDEO', 'https://example.com/video3.mp4', 60, 2),
(1, '集合框架详解', 'VIDEO', 'https://example.com/video4.mp4', 50, 3),
(1, '异常处理机制', 'VIDEO', 'https://example.com/video5.mp4', 40, 4),
(2, 'Spring Boot简介', 'VIDEO', 'https://example.com/video6.mp4', 25, 0),
(2, '创建第一个Spring Boot项目', 'VIDEO', 'https://example.com/video7.mp4', 35, 1),
(2, 'Spring Boot Web开发', 'VIDEO', 'https://example.com/video8.mp4', 55, 2),
(2, '数据库集成', 'VIDEO', 'https://example.com/video9.mp4', 45, 3),
(3, 'Vue.js基础概念', 'VIDEO', 'https://example.com/video10.mp4', 40, 0),
(3, '组件化开发', 'VIDEO', 'https://example.com/video11.mp4', 50, 1),
(3, '路由管理', 'VIDEO', 'https://example.com/video12.mp4', 35, 2);

-- 插入课程注册记录
INSERT INTO course_enrollments (user_id, course_id, progress_percentage) VALUES 
(4, 1, 75.0),
(4, 2, 30.0),
(5, 1, 100.0),
(5, 3, 45.0),
(4, 3, 20.0);