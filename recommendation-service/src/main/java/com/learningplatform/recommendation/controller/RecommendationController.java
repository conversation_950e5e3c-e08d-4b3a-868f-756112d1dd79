package com.learningplatform.recommendation.controller;

import com.learningplatform.recommendation.model.RecommendationModel;
import com.learningplatform.recommendation.model.UserBehaviorModel;
import com.learningplatform.recommendation.service.RecommendationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 推荐服务控制器
 */
@RestController
@RequestMapping("/api/recommendation")
@CrossOrigin(origins = "*")
public class RecommendationController {
    
    @Autowired
    private RecommendationService recommendationService;
    
    /**
     * 获取用户课程推荐
     */
    @GetMapping("/courses")
    public Map<String, Object> getCourseRecommendations(
            @RequestParam Long userId,
            @RequestParam(defaultValue = "5") Integer limit) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<RecommendationModel> recommendations = recommendationService.getUserRecommendations(userId, limit);
            
            response.put("success", true);
            response.put("data", recommendations);
            response.put("message", "获取推荐成功");
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取推荐失败: " + e.getMessage());
            response.put("data", null);
        }
        
        return response;
    }
    
    /**
     * 获取相关课程推荐
     */
    @GetMapping("/related/{courseId}")
    public Map<String, Object> getRelatedCourses(
            @PathVariable Long courseId,
            @RequestParam(defaultValue = "5") Integer limit) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<RecommendationModel> relatedCourses = recommendationService.getRelatedCourses(courseId, limit);
            
            response.put("success", true);
            response.put("data", relatedCourses);
            response.put("message", "获取相关课程推荐成功");
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取相关课程推荐失败: " + e.getMessage());
            response.put("data", null);
        }
        
        return response;
    }
    
    /**
     * 记录用户行为
     */
    @PostMapping("/behavior")
    public Map<String, Object> recordBehavior(@RequestBody UserBehaviorModel userBehavior) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            recommendationService.recordUserBehavior(userBehavior);
            
            response.put("success", true);
            response.put("message", "用户行为记录成功");
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "用户行为记录失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 处理推荐反馈
     */
    @PostMapping("/feedback")
    public Map<String, Object> processFeedback(@RequestBody Map<String, Object> feedbackData) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Long userId = Long.valueOf(feedbackData.get("userId").toString());
            Long courseId = Long.valueOf(feedbackData.get("courseId").toString());
            String feedback = feedbackData.get("feedback").toString();
            
            recommendationService.processFeedback(userId, courseId, feedback);
            
            response.put("success", true);
            response.put("message", "推荐反馈处理成功");
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "推荐反馈处理失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 生成新的推荐
     */
    @PostMapping("/generate")
    public Map<String, Object> generateRecommendations(
            @RequestParam Long userId,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<RecommendationModel> recommendations = recommendationService.generateRecommendations(userId, limit);
            
            response.put("success", true);
            response.put("data", recommendations);
            response.put("message", "推荐生成成功");
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "推荐生成失败: " + e.getMessage());
            response.put("data", null);
        }
        
        return response;
    }
    
    /**
     * 获取协同过滤推荐
     */
    @GetMapping("/collaborative")
    public Map<String, Object> getCollaborativeRecommendations(
            @RequestParam Long userId,
            @RequestParam(defaultValue = "5") Integer limit) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 获取用户行为数据
            List<UserBehaviorModel> userBehaviors = recommendationService.getUserBehaviors(userId);
            List<RecommendationModel> recommendations = recommendationService.getCollaborativeRecommendations(userId, userBehaviors, limit);
            
            response.put("success", true);
            response.put("data", recommendations);
            response.put("message", "获取协同过滤推荐成功");
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取协同过滤推荐失败: " + e.getMessage());
            response.put("data", null);
        }
        
        return response;
    }
    
    /**
     * 获取内容过滤推荐
     */
    @GetMapping("/content-based")
    public Map<String, Object> getContentBasedRecommendations(
            @RequestParam Long userId,
            @RequestParam(defaultValue = "5") Integer limit) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 获取用户行为数据
            List<UserBehaviorModel> userBehaviors = recommendationService.getUserBehaviors(userId);
            List<RecommendationModel> recommendations = recommendationService.getContentBasedRecommendations(userId, userBehaviors, limit);
            
            response.put("success", true);
            response.put("data", recommendations);
            response.put("message", "获取内容过滤推荐成功");
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取内容过滤推荐失败: " + e.getMessage());
            response.put("data", null);
        }
        
        return response;
    }
    
    /**
     * 获取混合推荐
     */
    @GetMapping("/hybrid")
    public Map<String, Object> getHybridRecommendations(
            @RequestParam Long userId,
            @RequestParam(defaultValue = "5") Integer limit) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 获取用户行为数据
            List<UserBehaviorModel> userBehaviors = recommendationService.getUserBehaviors(userId);
            List<RecommendationModel> recommendations = recommendationService.getHybridRecommendations(userId, userBehaviors, limit);
            
            response.put("success", true);
            response.put("data", recommendations);
            response.put("message", "获取混合推荐成功");
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取混合推荐失败: " + e.getMessage());
            response.put("data", null);
        }
        
        return response;
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "推荐服务运行正常");
        response.put("service", "recommendation-service");
        return response;
    }
}