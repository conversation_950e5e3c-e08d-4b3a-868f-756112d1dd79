package com.learningplatform.discussion.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.learningplatform.discussion.dto.*;

/**
 * 讨论服务接口
 */
public interface DiscussionService {
    
    /**
     * 创建讨论主题
     */
    DiscussionTopicResponse createTopic(Long userId, DiscussionTopicCreateRequest request);
    
    /**
     * 获取课程讨论主题列表
     */
    IPage<DiscussionTopicResponse> getTopicsByCourseId(Long courseId, Integer page, Integer size, String sortBy);
    
    /**
     * 获取热门讨论主题列表
     */
    IPage<DiscussionTopicResponse> getHotTopics(Long courseId, Integer page, Integer size);
    
    /**
     * 获取讨论主题详情
     */
    DiscussionTopicResponse getTopicById(Long topicId);
    
    /**
     * 搜索讨论主题
     */
    IPage<DiscussionTopicResponse> searchTopics(Long courseId, String keyword, Integer page, Integer size);
    
    /**
     * 创建讨论回复
     */
    DiscussionReplyResponse createReply(Long userId, DiscussionReplyCreateRequest request);
    
    /**
     * 获取主题回复列表
     */
    IPage<DiscussionReplyResponse> getRepliesByTopicId(Long topicId, Integer page, Integer size);
    
    /**
     * 获取主题的所有回复（包括嵌套结构）
     */
    java.util.List<DiscussionReplyResponse> getAllRepliesByTopicId(Long topicId);
    
    /**
     * 点赞回复
     */
    void likeReply(Long replyId, Long userId);
    
    /**
     * 取消点赞回复
     */
    void unlikeReply(Long replyId, Long userId);
    
    /**
     * 删除讨论主题
     */
    void deleteTopic(Long topicId, Long userId);
    
    /**
     * 删除讨论回复
     */
    void deleteReply(Long replyId, Long userId);
    
    /**
     * 置顶讨论主题
     */
    void pinTopic(Long topicId, Long userId);
    
    /**
     * 取消置顶讨论主题
     */
    void unpinTopic(Long topicId, Long userId);
}