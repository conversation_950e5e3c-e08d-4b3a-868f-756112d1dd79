package com.learningplatform.discussion.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.learningplatform.discussion.dto.*;
import com.learningplatform.discussion.service.DiscussionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 讨论控制器
 */
@RestController
@RequestMapping("/api/discussion")
public class DiscussionController {
    
    private static final Logger log = LoggerFactory.getLogger(DiscussionController.class);
    
    private final DiscussionService discussionService;
    
    public DiscussionController(DiscussionService discussionService) {
        this.discussionService = discussionService;
    }
    
    /**
     * 创建讨论主题
     */
    @PostMapping("/topics")
    public ResponseEntity<Map<String, Object>> createTopic(
            @RequestHeader("X-User-Id") Long userId,
            @Valid @RequestBody DiscussionTopicCreateRequest request) {
        
        log.info("创建讨论主题请求，用户ID: {}, 请求: {}", userId, request);
        
        try {
            DiscussionTopicResponse response = discussionService.createTopic(userId, request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "讨论主题创建成功");
            result.put("data", response);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("创建讨论主题失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "创建讨论主题失败: " + e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 获取课程讨论主题列表
     */
    @GetMapping("/courses/{courseId}/topics")
    public ResponseEntity<Map<String, Object>> getTopicsByCourseId(
            @PathVariable Long courseId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(defaultValue = "latest") String sortBy) {
        
        log.info("获取课程讨论主题列表，课程ID: {}, 页码: {}, 大小: {}, 排序: {}", courseId, page, size, sortBy);
        
        try {
            IPage<DiscussionTopicResponse> topicPage = discussionService.getTopicsByCourseId(courseId, page, size, sortBy);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "获取讨论主题列表成功");
            result.put("data", topicPage);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取讨论主题列表失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取讨论主题列表失败: " + e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 获取热门讨论主题列表
     */
    @GetMapping("/courses/{courseId}/topics/hot")
    public ResponseEntity<Map<String, Object>> getHotTopics(
            @PathVariable Long courseId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        log.info("获取热门讨论主题列表，课程ID: {}, 页码: {}, 大小: {}", courseId, page, size);
        
        try {
            IPage<DiscussionTopicResponse> topicPage = discussionService.getHotTopics(courseId, page, size);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "获取热门讨论主题列表成功");
            result.put("data", topicPage);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取热门讨论主题列表失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取热门讨论主题列表失败: " + e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 获取讨论主题详情
     */
    @GetMapping("/topics/{topicId}")
    public ResponseEntity<Map<String, Object>> getTopicById(@PathVariable Long topicId) {
        log.info("获取讨论主题详情，主题ID: {}", topicId);
        
        try {
            DiscussionTopicResponse response = discussionService.getTopicById(topicId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "获取讨论主题详情成功");
            result.put("data", response);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取讨论主题详情失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取讨论主题详情失败: " + e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 搜索讨论主题
     */
    @GetMapping("/courses/{courseId}/topics/search")
    public ResponseEntity<Map<String, Object>> searchTopics(
            @PathVariable Long courseId,
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        log.info("搜索讨论主题，课程ID: {}, 关键词: {}", courseId, keyword);
        
        try {
            IPage<DiscussionTopicResponse> topicPage = discussionService.searchTopics(courseId, keyword, page, size);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "搜索讨论主题成功");
            result.put("data", topicPage);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("搜索讨论主题失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "搜索讨论主题失败: " + e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 创建讨论回复
     */
    @PostMapping("/replies")
    public ResponseEntity<Map<String, Object>> createReply(
            @RequestHeader("X-User-Id") Long userId,
            @Valid @RequestBody DiscussionReplyCreateRequest request) {
        
        log.info("创建讨论回复请求，用户ID: {}, 请求: {}", userId, request);
        
        try {
            DiscussionReplyResponse response = discussionService.createReply(userId, request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "讨论回复创建成功");
            result.put("data", response);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("创建讨论回复失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "创建讨论回复失败: " + e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 获取主题回复列表
     */
    @GetMapping("/topics/{topicId}/replies")
    public ResponseEntity<Map<String, Object>> getRepliesByTopicId(
            @PathVariable Long topicId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        log.info("获取主题回复列表，主题ID: {}", topicId);
        
        try {
            IPage<DiscussionReplyResponse> replyPage = discussionService.getRepliesByTopicId(topicId, page, size);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "获取回复列表成功");
            result.put("data", replyPage);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取回复列表失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取回复列表失败: " + e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 获取主题的所有回复（嵌套结构）
     */
    @GetMapping("/topics/{topicId}/replies/all")
    public ResponseEntity<Map<String, Object>> getAllRepliesByTopicId(@PathVariable Long topicId) {
        log.info("获取主题所有回复（嵌套结构），主题ID: {}", topicId);
        
        try {
            List<DiscussionReplyResponse> replies = discussionService.getAllRepliesByTopicId(topicId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "获取所有回复成功");
            result.put("data", replies);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取所有回复失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取所有回复失败: " + e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 点赞回复
     */
    @PostMapping("/replies/{replyId}/like")
    public ResponseEntity<Map<String, Object>> likeReply(
            @PathVariable Long replyId,
            @RequestHeader("X-User-Id") Long userId) {
        
        log.info("点赞回复，回复ID: {}, 用户ID: {}", replyId, userId);
        
        try {
            discussionService.likeReply(replyId, userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "点赞成功");
            result.put("data", null);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("点赞失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "点赞失败: " + e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 取消点赞回复
     */
    @DeleteMapping("/replies/{replyId}/like")
    public ResponseEntity<Map<String, Object>> unlikeReply(
            @PathVariable Long replyId,
            @RequestHeader("X-User-Id") Long userId) {
        
        log.info("取消点赞回复，回复ID: {}, 用户ID: {}", replyId, userId);
        
        try {
            discussionService.unlikeReply(replyId, userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "取消点赞成功");
            result.put("data", null);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("取消点赞失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "取消点赞失败: " + e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 删除讨论主题
     */
    @DeleteMapping("/topics/{topicId}")
    public ResponseEntity<Map<String, Object>> deleteTopic(
            @PathVariable Long topicId,
            @RequestHeader("X-User-Id") Long userId) {
        
        log.info("删除讨论主题，主题ID: {}, 用户ID: {}", topicId, userId);
        
        try {
            discussionService.deleteTopic(topicId, userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "删除主题成功");
            result.put("data", null);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除主题失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "删除主题失败: " + e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 删除讨论回复
     */
    @DeleteMapping("/replies/{replyId}")
    public ResponseEntity<Map<String, Object>> deleteReply(
            @PathVariable Long replyId,
            @RequestHeader("X-User-Id") Long userId) {
        
        log.info("删除讨论回复，回复ID: {}, 用户ID: {}", replyId, userId);
        
        try {
            discussionService.deleteReply(replyId, userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "删除回复成功");
            result.put("data", null);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除回复失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "删除回复失败: " + e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 置顶讨论主题
     */
    @PostMapping("/topics/{topicId}/pin")
    public ResponseEntity<Map<String, Object>> pinTopic(
            @PathVariable Long topicId,
            @RequestHeader("X-User-Id") Long userId) {
        
        log.info("置顶讨论主题，主题ID: {}, 用户ID: {}", topicId, userId);
        
        try {
            discussionService.pinTopic(topicId, userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "置顶主题成功");
            result.put("data", null);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("置顶主题失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "置顶主题失败: " + e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 取消置顶讨论主题
     */
    @DeleteMapping("/topics/{topicId}/pin")
    public ResponseEntity<Map<String, Object>> unpinTopic(
            @PathVariable Long topicId,
            @RequestHeader("X-User-Id") Long userId) {
        
        log.info("取消置顶讨论主题，主题ID: {}, 用户ID: {}", topicId, userId);
        
        try {
            discussionService.unpinTopic(topicId, userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "取消置顶成功");
            result.put("data", null);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("取消置顶失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "取消置顶失败: " + e.getMessage());
            result.put("data", null);
            
            return ResponseEntity.badRequest().body(result);
        }
    }
}