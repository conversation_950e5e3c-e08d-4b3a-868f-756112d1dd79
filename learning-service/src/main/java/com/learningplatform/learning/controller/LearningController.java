package com.learningplatform.learning.controller;

import com.learningplatform.common.response.Result;
import com.learningplatform.learning.entity.CourseEnrollment;
import com.learningplatform.learning.entity.LearningProgress;
import com.learningplatform.learning.service.CourseEnrollmentService;
import com.learningplatform.learning.service.LearningProgressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 学习进度控制器
 */
@RestController
@RequestMapping("/api/learning")
public class LearningController {
    
    @Autowired
    private CourseEnrollmentService courseEnrollmentService;
    
    @Autowired
    private LearningProgressService learningProgressService;
    
    /**
     * 用户注册课程
     */
    @PostMapping("/enroll")
    public Result<CourseEnrollment> enrollCourse(@RequestParam Long userId, @RequestParam Long courseId) {
        try {
            CourseEnrollment enrollment = courseEnrollmentService.enrollCourse(userId, courseId);
            return Result.success(enrollment);
        } catch (Exception e) {
            return Result.error("注册课程失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户已注册的课程列表
     */
    @GetMapping("/enrollments")
    public Result<List<CourseEnrollment>> getUserEnrollments(@RequestParam Long userId) {
        List<CourseEnrollment> enrollments = courseEnrollmentService.getUserEnrollments(userId);
        return Result.success(enrollments);
    }
    
    /**
     * 检查用户是否已注册某课程
     */
    @GetMapping("/enrolled")
    public Result<Boolean> isEnrolled(@RequestParam Long userId, @RequestParam Long courseId) {
        boolean enrolled = courseEnrollmentService.isEnrolled(userId, courseId);
        return Result.success(enrolled);
    }
    
    /**
     * 获取学习进度
     */
    @GetMapping("/progress/{courseId}")
    public Result<List<LearningProgress>> getCourseProgress(@PathVariable Long courseId, @RequestParam Long userId) {
        List<LearningProgress> progress = learningProgressService.getCourseProgress(userId, courseId);
        return Result.success(progress);
    }
    
    /**
     * 更新学习进度
     */
    @PostMapping("/progress")
    public Result<LearningProgress> updateProgress(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            Long courseId = Long.valueOf(request.get("courseId").toString());
            Long chapterId = Long.valueOf(request.get("chapterId").toString());
            Integer watchDurationSeconds = Integer.valueOf(request.get("watchDurationSeconds").toString());
            Integer lastPositionSeconds = Integer.valueOf(request.get("lastPositionSeconds").toString());
            
            LearningProgress progress = learningProgressService.updateProgress(
                userId, courseId, chapterId, watchDurationSeconds, lastPositionSeconds);
            return Result.success(progress);
        } catch (Exception e) {
            return Result.error("更新学习进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 完成章节学习
     */
    @PostMapping("/progress/complete")
    public Result<String> completeChapter(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            Long courseId = Long.valueOf(request.get("courseId").toString());
            Long chapterId = Long.valueOf(request.get("chapterId").toString());
            
            learningProgressService.completeChapter(userId, courseId, chapterId);
            return Result.success("章节完成成功");
        } catch (Exception e) {
            return Result.error("完成章节失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取学习仪表板数据
     */
    @GetMapping("/dashboard")
    public Result<Map<String, Object>> getLearningDashboard(@RequestParam Long userId) {
        Map<String, Object> dashboard = learningProgressService.getLearningDashboard(userId);
        return Result.success(dashboard);
    }
    
    /**
     * 获取学习统计数据
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getLearningStatistics(@RequestParam Long userId) {
        Map<String, Object> statistics = learningProgressService.getLearningStatistics(userId);
        return Result.success(statistics);
    }
}