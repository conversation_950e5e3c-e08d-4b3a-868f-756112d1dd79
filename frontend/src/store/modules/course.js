import api from '../../api/course'

const state = {
  courses: [],
  currentCourse: null,
  categories: [],
  searchResults: []
}

const mutations = {
  SET_COURSES(state, courses) {
    state.courses = courses
  },
  SET_CURRENT_COURSE(state, course) {
    state.currentCourse = course
  },
  SET_CATEGORIES(state, categories) {
    state.categories = categories
  },
  SET_SEARCH_RESULTS(state, results) {
    state.searchResults = results
  },
  ADD_COURSE(state, course) {
    state.courses.push(course)
  },
  UPDATE_COURSE(state, updatedCourse) {
    const index = state.courses.findIndex(course => course.id === updatedCourse.id)
    if (index !== -1) {
      state.courses.splice(index, 1, updatedCourse)
    }
  },
  REMOVE_COURSE(state, courseId) {
    state.courses = state.courses.filter(course => course.id !== courseId)
  }
}

const actions = {
  async fetchCourses({ commit }, params = {}) {
    try {
      const response = await api.getCourses(params)
      commit('SET_COURSES', response.data)
      return response
    } catch (error) {
      throw error
    }
  },
  
  async fetchCourse({ commit }, courseId) {
    try {
      const response = await api.getCourse(courseId)
      commit('SET_CURRENT_COURSE', response.data)
      return response
    } catch (error) {
      throw error
    }
  },
  
  async searchCourses({ commit }, searchParams) {
    try {
      const response = await api.searchCourses(searchParams)
      commit('SET_SEARCH_RESULTS', response.data)
      return response
    } catch (error) {
      throw error
    }
  },
  
  async createCourse({ commit }, courseData) {
    try {
      const response = await api.createCourse(courseData)
      commit('ADD_COURSE', response.data)
      return response
    } catch (error) {
      throw error
    }
  },
  
  async updateCourse({ commit }, { courseId, courseData }) {
    try {
      const response = await api.updateCourse(courseId, courseData)
      commit('UPDATE_COURSE', response.data)
      return response
    } catch (error) {
      throw error
    }
  },
  
  async deleteCourse({ commit }, courseId) {
    try {
      const response = await api.deleteCourse(courseId)
      commit('REMOVE_COURSE', courseId)
      return response
    } catch (error) {
      throw error
    }
  },
  
  async enrollCourse({ commit }, courseId) {
    try {
      const response = await api.enrollCourse(courseId)
      return response
    } catch (error) {
      throw error
    }
  }
}

const getters = {
  courses: state => state.courses,
  currentCourse: state => state.currentCourse,
  categories: state => state.categories,
  searchResults: state => state.searchResults
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}