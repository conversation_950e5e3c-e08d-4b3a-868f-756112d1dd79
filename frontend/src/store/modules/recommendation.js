import api from '../../api/recommendation'

const state = {
  recommendations: [],
  relatedCourses: {}
}

const mutations = {
  SET_RECOMMENDATIONS(state, recommendations) {
    state.recommendations = recommendations
  },
  SET_RELATED_COURSES(state, { courseId, courses }) {
    state.relatedCourses[courseId] = courses
  }
}

const actions = {
  async fetchRecommendations({ commit }) {
    try {
      const response = await api.getRecommendations()
      commit('SET_RECOMMENDATIONS', response.data)
      return response
    } catch (error) {
      throw error
    }
  },
  
  async fetchRelatedCourses({ commit }, courseId) {
    try {
      const response = await api.getRelatedCourses(courseId)
      commit('SET_RELATED_COURSES', { courseId, courses: response.data })
      return response
    } catch (error) {
      throw error
    }
  },
  
  async submitFeedback({ commit }, feedbackData) {
    try {
      const response = await api.submitFeedback(feedbackData)
      return response
    } catch (error) {
      throw error
    }
  }
}

const getters = {
  recommendations: state => state.recommendations,
  relatedCourses: state => state.relatedCourses,
  getRelatedCourses: state => courseId => state.relatedCourses[courseId] || []
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}