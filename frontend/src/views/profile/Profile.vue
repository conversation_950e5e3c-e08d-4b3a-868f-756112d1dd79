<template>
  <div class="profile-page">
    <div class="container">
      <div class="profile-header">
        <h1>个人资料</h1>
        <p>管理您的账户信息和偏好设置</p>
      </div>
      
      <div class="profile-content">
        <div class="profile-card">
          <div class="profile-avatar">
            <div class="avatar-placeholder">
              {{ user?.username?.charAt(0)?.toUpperCase() || 'U' }}
            </div>
            <button class="avatar-upload-btn">
              更换头像
            </button>
          </div>
          
          <form @submit.prevent="handleUpdateProfile" class="profile-form">
            <div class="form-row">
              <div class="form-group">
                <label for="username">用户名</label>
                <input
                  id="username"
                  v-model="form.username"
                  type="text"
                  class="form-control"
                  :class="{ 'error': errors.username }"
                  readonly
                >
                <small class="form-help">用户名不可修改</small>
              </div>
              
              <div class="form-group">
                <label for="email">邮箱</label>
                <input
                  id="email"
                  v-model="form.email"
                  type="email"
                  class="form-control"
                  :class="{ 'error': errors.email }"
                  placeholder="请输入邮箱地址"
                >
                <span v-if="errors.email" class="error-message">{{ errors.email }}</span>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="realName">真实姓名</label>
                <input
                  id="realName"
                  v-model="form.realName"
                  type="text"
                  class="form-control"
                  placeholder="请输入真实姓名"
                >
              </div>
              
              <div class="form-group">
                <label for="phone">手机号码</label>
                <input
                  id="phone"
                  v-model="form.phone"
                  type="tel"
                  class="form-control"
                  placeholder="请输入手机号码"
                >
              </div>
            </div>
            
            <div class="form-group">
              <label for="bio">个人简介</label>
              <textarea
                id="bio"
                v-model="form.bio"
                class="form-control"
                rows="4"
                placeholder="介绍一下自己..."
              ></textarea>
            </div>
            
            <div class="form-group">
              <label for="birthDate">出生日期</label>
              <input
                id="birthDate"
                v-model="form.birthDate"
                type="date"
                class="form-control"
              >
            </div>
            
            <div class="form-actions">
              <button 
                type="submit" 
                class="btn btn-primary"
                :disabled="loading"
              >
                <span v-if="loading">保存中...</span>
                <span v-else>保存更改</span>
              </button>
              
              <button 
                type="button" 
                class="btn btn-outline"
                @click="resetForm"
              >
                重置
              </button>
            </div>
          </form>
        </div>
        
        <!-- 密码修改区域 -->
        <div class="password-card">
          <h3>修改密码</h3>
          <form @submit.prevent="handleChangePassword" class="password-form">
            <div class="form-group">
              <label for="currentPassword">当前密码</label>
              <input
                id="currentPassword"
                v-model="passwordForm.currentPassword"
                type="password"
                class="form-control"
                :class="{ 'error': passwordErrors.currentPassword }"
                placeholder="请输入当前密码"
              >
              <span v-if="passwordErrors.currentPassword" class="error-message">
                {{ passwordErrors.currentPassword }}
              </span>
            </div>
            
            <div class="form-group">
              <label for="newPassword">新密码</label>
              <input
                id="newPassword"
                v-model="passwordForm.newPassword"
                type="password"
                class="form-control"
                :class="{ 'error': passwordErrors.newPassword }"
                placeholder="请输入新密码（至少6位）"
              >
              <span v-if="passwordErrors.newPassword" class="error-message">
                {{ passwordErrors.newPassword }}
              </span>
            </div>
            
            <div class="form-group">
              <label for="confirmNewPassword">确认新密码</label>
              <input
                id="confirmNewPassword"
                v-model="passwordForm.confirmNewPassword"
                type="password"
                class="form-control"
                :class="{ 'error': passwordErrors.confirmNewPassword }"
                placeholder="请再次输入新密码"
              >
              <span v-if="passwordErrors.confirmNewPassword" class="error-message">
                {{ passwordErrors.confirmNewPassword }}
              </span>
            </div>
            
            <button 
              type="submit" 
              class="btn btn-primary"
              :disabled="passwordLoading"
            >
              <span v-if="passwordLoading">修改中...</span>
              <span v-else>修改密码</span>
            </button>
          </form>
        </div>
      </div>
      
      <div v-if="error" class="error-alert">
        {{ error }}
      </div>
      
      <div v-if="success" class="success-alert">
        {{ success }}
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'Profile',
  setup() {
    const store = useStore()
    
    const user = computed(() => store.getters['auth/user'])
    const error = computed(() => store.state.error)
    
    const form = reactive({
      username: '',
      email: '',
      realName: '',
      phone: '',
      bio: '',
      birthDate: ''
    })
    
    const passwordForm = reactive({
      currentPassword: '',
      newPassword: '',
      confirmNewPassword: ''
    })
    
    const errors = reactive({})
    const passwordErrors = reactive({})
    const loading = ref(false)
    const passwordLoading = ref(false)
    const success = ref('')
    
    const loadUserProfile = async () => {
      try {
        await store.dispatch('auth/fetchProfile')
        const userData = store.getters['auth/user']
        if (userData) {
          form.username = userData.username || ''
          form.email = userData.email || ''
          form.realName = userData.realName || ''
          form.phone = userData.phone || ''
          form.bio = userData.bio || ''
          form.birthDate = userData.birthDate || ''
        }
      } catch (error) {
        console.error('加载用户资料失败:', error)
      }
    }
    
    const validateForm = () => {
      const newErrors = {}
      
      if (!form.email.trim()) {
        newErrors.email = '请输入邮箱'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
        newErrors.email = '请输入有效的邮箱地址'
      }
      
      Object.assign(errors, newErrors)
      return Object.keys(newErrors).length === 0
    }
    
    const validatePasswordForm = () => {
      const newErrors = {}
      
      if (!passwordForm.currentPassword.trim()) {
        newErrors.currentPassword = '请输入当前密码'
      }
      
      if (!passwordForm.newPassword.trim()) {
        newErrors.newPassword = '请输入新密码'
      } else if (passwordForm.newPassword.length < 6) {
        newErrors.newPassword = '密码长度至少6位'
      }
      
      if (!passwordForm.confirmNewPassword.trim()) {
        newErrors.confirmNewPassword = '请确认新密码'
      } else if (passwordForm.newPassword !== passwordForm.confirmNewPassword) {
        newErrors.confirmNewPassword = '两次输入的密码不一致'
      }
      
      Object.assign(passwordErrors, newErrors)
      return Object.keys(newErrors).length === 0
    }
    
    const handleUpdateProfile = async () => {
      Object.keys(errors).forEach(key => delete errors[key])
      store.dispatch('clearError')
      success.value = ''
      
      if (!validateForm()) {
        return
      }
      
      loading.value = true
      
      try {
        await store.dispatch('auth/updateProfile', {
          email: form.email,
          realName: form.realName,
          phone: form.phone,
          bio: form.bio,
          birthDate: form.birthDate
        })
        
        success.value = '个人资料更新成功！'
        setTimeout(() => {
          success.value = ''
        }, 3000)
        
      } catch (error) {
        console.error('更新个人资料失败:', error)
        store.dispatch('setError', error.response?.data?.message || '更新失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
    
    const handleChangePassword = async () => {
      Object.keys(passwordErrors).forEach(key => delete passwordErrors[key])
      store.dispatch('clearError')
      success.value = ''
      
      if (!validatePasswordForm()) {
        return
      }
      
      passwordLoading.value = true
      
      try {
        // 这里需要调用修改密码的API
        // await store.dispatch('auth/changePassword', {
        //   currentPassword: passwordForm.currentPassword,
        //   newPassword: passwordForm.newPassword
        // })
        
        success.value = '密码修改成功！'
        
        // 清空密码表单
        passwordForm.currentPassword = ''
        passwordForm.newPassword = ''
        passwordForm.confirmNewPassword = ''
        
        setTimeout(() => {
          success.value = ''
        }, 3000)
        
      } catch (error) {
        console.error('修改密码失败:', error)
        store.dispatch('setError', error.response?.data?.message || '密码修改失败，请稍后重试')
      } finally {
        passwordLoading.value = false
      }
    }
    
    const resetForm = () => {
      loadUserProfile()
    }
    
    onMounted(() => {
      loadUserProfile()
    })
    
    return {
      user,
      form,
      passwordForm,
      errors,
      passwordErrors,
      loading,
      passwordLoading,
      success,
      error,
      handleUpdateProfile,
      handleChangePassword,
      resetForm
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.profile-header {
  text-align: center;
  margin-bottom: 40px;
}

.profile-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.profile-header p {
  color: #7f8c8d;
  margin: 0;
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.profile-card,
.password-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.profile-avatar {
  text-align: center;
  margin-bottom: 30px;
}

.avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  font-weight: bold;
  margin: 0 auto 15px;
}

.avatar-upload-btn {
  background: none;
  border: 1px solid #3498db;
  color: #3498db;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.avatar-upload-btn:hover {
  background-color: #3498db;
  color: white;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #2c3e50;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
}

.form-control:read-only {
  background-color: #f8f9fa;
  color: #6c757d;
}

.form-control.error {
  border-color: #e74c3c;
}

.form-help {
  color: #7f8c8d;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

.error-message {
  color: #e74c3c;
  font-size: 14px;
  margin-top: 5px;
  display: block;
}

.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.password-card h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 20px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2980b9;
}

.btn-primary:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.btn-outline {
  background-color: transparent;
  color: #3498db;
  border: 2px solid #3498db;
}

.btn-outline:hover {
  background-color: #3498db;
  color: white;
}

.error-alert {
  background-color: #fdf2f2;
  color: #e74c3c;
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #fecaca;
}

.success-alert {
  background-color: #f0f9ff;
  color: #059669;
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #a7f3d0;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .profile-card,
  .password-card {
    padding: 20px;
  }
}
</style>