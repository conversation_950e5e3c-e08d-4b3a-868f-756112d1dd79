<template>
  <div class="course-list-page">
    <div class="container">
      <div class="page-header">
        <h1>课程中心</h1>
        <p>发现适合您的优质课程</p>
      </div>
      
      <!-- 搜索和筛选 -->
      <div class="search-filters">
        <div class="search-box">
          <input
            v-model="searchQuery"
            type="text"
            class="form-control"
            placeholder="搜索课程..."
            @keyup.enter="handleSearch"
          >
          <button class="search-btn" @click="handleSearch">
            🔍
          </button>
        </div>
        
        <div class="filters">
          <select v-model="selectedCategory" class="form-control" @change="handleFilter">
            <option value="">所有分类</option>
            <option value="programming">编程开发</option>
            <option value="design">设计创意</option>
            <option value="business">商业管理</option>
            <option value="language">语言学习</option>
          </select>
          
          <select v-model="selectedLevel" class="form-control" @change="handleFilter">
            <option value="">所有难度</option>
            <option value="BEGINNER">初级</option>
            <option value="INTERMEDIATE">中级</option>
            <option value="ADVANCED">高级</option>
          </select>
          
          <select v-model="sortBy" class="form-control" @change="handleFilter">
            <option value="created_at">最新发布</option>
            <option value="popular">最受欢迎</option>
            <option value="rating">评分最高</option>
          </select>
        </div>
      </div>
      
      <!-- 课程列表 -->
      <div class="courses-section">
        <div v-if="loading" class="loading">
          加载中...
        </div>
        
        <div v-else-if="courses.length === 0" class="empty-state">
          <div class="empty-icon">📚</div>
          <h3>暂无课程</h3>
          <p>没有找到符合条件的课程，请尝试调整搜索条件</p>
        </div>
        
        <div v-else class="courses-grid">
          <div 
            v-for="course in courses" 
            :key="course.id" 
            class="course-card"
            @click="goToCourse(course.id)"
          >
            <div class="course-image">
              <img :src="course.coverImage || '/placeholder-course.jpg'" :alt="course.title">
              <div class="course-level">{{ getLevelText(course.difficultyLevel) }}</div>
            </div>
            
            <div class="course-content">
              <h3 class="course-title">{{ course.title }}</h3>
              <p class="course-description">{{ course.description }}</p>
              
              <div class="course-meta">
                <div class="course-teacher">
                  👨‍🏫 {{ course.teacherName || '讲师' }}
                </div>
                <div class="course-stats">
                  <span class="students-count">{{ course.studentsCount || 0 }}人学习</span>
                  <span class="rating">⭐ {{ course.rating || '暂无评分' }}</span>
                </div>
              </div>
              
              <div class="course-footer">
                <div class="course-price">
                  <span v-if="course.price === 0" class="free">免费</span>
                  <span v-else class="price">¥{{ course.price }}</span>
                </div>
                <button class="btn btn-primary btn-sm" @click.stop="handleEnroll(course.id)">
                  立即学习
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination">
          <button 
            class="btn btn-outline"
            :disabled="currentPage === 1"
            @click="changePage(currentPage - 1)"
          >
            上一页
          </button>
          
          <span class="page-info">
            第 {{ currentPage }} 页，共 {{ totalPages }} 页
          </span>
          
          <button 
            class="btn btn-outline"
            :disabled="currentPage === totalPages"
            @click="changePage(currentPage + 1)"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'CourseList',
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const searchQuery = ref('')
    const selectedCategory = ref('')
    const selectedLevel = ref('')
    const sortBy = ref('created_at')
    const currentPage = ref(1)
    const pageSize = ref(12)
    const loading = ref(false)
    
    const courses = computed(() => store.getters['course/courses'])
    const totalPages = ref(1)
    
    const getLevelText = (level) => {
      const levelMap = {
        'BEGINNER': '初级',
        'INTERMEDIATE': '中级',
        'ADVANCED': '高级'
      }
      return levelMap[level] || level
    }
    
    const fetchCourses = async () => {
      loading.value = true
      try {
        const params = {
          page: currentPage.value,
          size: pageSize.value,
          sort: sortBy.value
        }
        
        if (selectedCategory.value) {
          params.category = selectedCategory.value
        }
        
        if (selectedLevel.value) {
          params.level = selectedLevel.value
        }
        
        if (searchQuery.value.trim()) {
          params.keyword = searchQuery.value.trim()
        }
        
        const response = await store.dispatch('course/fetchCourses', params)
        
        // 假设后端返回分页信息
        if (response.data && response.data.totalPages) {
          totalPages.value = response.data.totalPages
        }
        
      } catch (error) {
        console.error('获取课程列表失败:', error)
        store.dispatch('setError', '获取课程列表失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
    
    const handleSearch = () => {
      currentPage.value = 1
      fetchCourses()
    }
    
    const handleFilter = () => {
      currentPage.value = 1
      fetchCourses()
    }
    
    const changePage = (page) => {
      currentPage.value = page
      fetchCourses()
    }
    
    const goToCourse = (courseId) => {
      router.push(`/course/${courseId}`)
    }
    
    const handleEnroll = async (courseId) => {
      try {
        await store.dispatch('course/enrollCourse', courseId)
        store.dispatch('setError', null)
        // 可以显示成功消息或跳转到学习页面
        router.push(`/learning/course/${courseId}`)
      } catch (error) {
        console.error('报名失败:', error)
        if (error.response?.status === 401) {
          router.push('/login')
        } else {
          store.dispatch('setError', error.response?.data?.message || '报名失败，请稍后重试')
        }
      }
    }
    
    onMounted(() => {
      fetchCourses()
    })
    
    return {
      searchQuery,
      selectedCategory,
      selectedLevel,
      sortBy,
      currentPage,
      totalPages,
      loading,
      courses,
      getLevelText,
      handleSearch,
      handleFilter,
      changePage,
      goToCourse,
      handleEnroll
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 36px;
}

.page-header p {
  color: #7f8c8d;
  font-size: 18px;
}

.search-filters {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.search-box {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.search-box .form-control {
  flex: 1;
}

.search-btn {
  padding: 10px 20px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
}

.search-btn:hover {
  background-color: #2980b9;
}

.filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.courses-section {
  min-height: 400px;
}

.loading {
  text-align: center;
  padding: 60px 0;
  font-size: 18px;
  color: #7f8c8d;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.empty-state p {
  color: #7f8c8d;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.course-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
}

.course-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.course-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-level {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0,0,0,0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.course-content {
  padding: 20px;
}

.course-title {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
}

.course-description {
  color: #7f8c8d;
  margin-bottom: 15px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-meta {
  margin-bottom: 15px;
}

.course-teacher {
  color: #34495e;
  font-size: 14px;
  margin-bottom: 8px;
}

.course-stats {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #7f8c8d;
}

.course-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #ecf0f1;
}

.course-price .free {
  color: #27ae60;
  font-weight: 600;
}

.course-price .price {
  color: #e74c3c;
  font-weight: 600;
  font-size: 18px;
}

.btn-sm {
  padding: 8px 16px;
  font-size: 14px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 40px;
}

.page-info {
  color: #7f8c8d;
  font-size: 14px;
}

@media (max-width: 768px) {
  .courses-grid {
    grid-template-columns: 1fr;
  }
  
  .search-filters {
    padding: 20px;
  }
  
  .filters {
    grid-template-columns: 1fr;
  }
  
  .pagination {
    flex-direction: column;
    gap: 10px;
  }
}
</style>