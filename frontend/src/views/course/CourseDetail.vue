<template>
  <div class="course-detail-page">
    <div class="container">
      <div v-if="loading" class="loading">
        加载中...
      </div>
      
      <div v-else-if="course" class="course-detail">
        <!-- 课程头部信息 -->
        <div class="course-header">
          <div class="course-info">
            <div class="breadcrumb">
              <router-link to="/courses">课程中心</router-link>
              <span> / </span>
              <span>{{ course.title }}</span>
            </div>
            
            <h1 class="course-title">{{ course.title }}</h1>
            <p class="course-description">{{ course.description }}</p>
            
            <div class="course-meta">
              <div class="meta-item">
                <span class="meta-label">讲师：</span>
                <span class="meta-value">{{ course.teacherName || '未知讲师' }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">难度：</span>
                <span class="meta-value">{{ getLevelText(course.difficultyLevel) }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">学习人数：</span>
                <span class="meta-value">{{ course.studentsCount || 0 }}人</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">评分：</span>
                <span class="meta-value">⭐ {{ course.rating || '暂无评分' }}</span>
              </div>
            </div>
            
            <div class="course-actions">
              <div class="price-info">
                <span v-if="course.price === 0" class="free-price">免费</span>
                <span v-else class="price">¥{{ course.price }}</span>
              </div>
              
              <button 
                class="btn btn-primary btn-large"
                @click="handleEnroll"
                :disabled="enrolling"
              >
                <span v-if="enrolling">报名中...</span>
                <span v-else>立即报名</span>
              </button>
            </div>
          </div>
          
          <div class="course-image">
            <img :src="course.coverImage || '/placeholder-course.jpg'" :alt="course.title">
          </div>
        </div>
        
        <!-- 课程内容 -->
        <div class="course-content">
          <div class="content-tabs">
            <button 
              class="tab-btn"
              :class="{ active: activeTab === 'chapters' }"
              @click="activeTab = 'chapters'"
            >
              课程章节
            </button>
            <button 
              class="tab-btn"
              :class="{ active: activeTab === 'discussion' }"
              @click="activeTab = 'discussion'"
            >
              课程讨论
            </button>
            <button 
              class="tab-btn"
              :class="{ active: activeTab === 'related' }"
              @click="activeTab = 'related'"
            >
              相关推荐
            </button>
          </div>
          
          <!-- 课程章节 -->
          <div v-if="activeTab === 'chapters'" class="tab-content">
            <div class="chapters-list">
              <div v-if="chapters.length === 0" class="empty-chapters">
                <p>暂无课程章节</p>
              </div>
              <div v-else>
                <div 
                  v-for="(chapter, index) in chapters" 
                  :key="chapter.id"
                  class="chapter-item"
                >
                  <div class="chapter-header">
                    <div class="chapter-number">{{ index + 1 }}</div>
                    <div class="chapter-info">
                      <h3 class="chapter-title">{{ chapter.title }}</h3>
                      <div class="chapter-meta">
                        <span class="chapter-type">{{ getContentTypeText(chapter.contentType) }}</span>
                        <span v-if="chapter.durationMinutes" class="chapter-duration">
                          {{ chapter.durationMinutes }}分钟
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="chapter-actions">
                    <button class="btn btn-outline btn-sm">
                      预览
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 课程讨论 -->
          <div v-if="activeTab === 'discussion'" class="tab-content">
            <div class="discussion-section">
              <div class="discussion-header">
                <h3>课程讨论</h3>
                <button class="btn btn-primary" @click="showCreateDiscussion = true">
                  发起讨论
                </button>
              </div>
              
              <div class="discussions-list">
                <div v-if="discussions.length === 0" class="empty-discussions">
                  <p>暂无讨论，快来发起第一个讨论吧！</p>
                </div>
                <div v-else>
                  <div 
                    v-for="discussion in discussions" 
                    :key="discussion.id"
                    class="discussion-item"
                    @click="goToDiscussion(discussion.id)"
                  >
                    <div class="discussion-content">
                      <h4 class="discussion-title">{{ discussion.title }}</h4>
                      <p class="discussion-preview">{{ discussion.content }}</p>
                      <div class="discussion-meta">
                        <span class="author">{{ discussion.authorName }}</span>
                        <span class="time">{{ formatTime(discussion.createdAt) }}</span>
                        <span class="replies">{{ discussion.repliesCount }}回复</span>
                        <span class="likes">{{ discussion.likesCount }}点赞</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 相关推荐 -->
          <div v-if="activeTab === 'related'" class="tab-content">
            <div class="related-courses">
              <h3>相关课程推荐</h3>
              <div class="related-courses-grid">
                <div 
                  v-for="relatedCourse in relatedCourses" 
                  :key="relatedCourse.id"
                  class="related-course-card"
                  @click="goToCourse(relatedCourse.id)"
                >
                  <img :src="relatedCourse.coverImage || '/placeholder-course.jpg'" :alt="relatedCourse.title">
                  <div class="related-course-info">
                    <h4>{{ relatedCourse.title }}</h4>
                    <p>{{ relatedCourse.description }}</p>
                    <div class="related-course-meta">
                      <span class="price">
                        {{ relatedCourse.price === 0 ? '免费' : `¥${relatedCourse.price}` }}
                      </span>
                      <span class="students">{{ relatedCourse.studentsCount || 0 }}人学习</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="error-state">
        <h3>课程不存在</h3>
        <p>您访问的课程可能已被删除或不存在</p>
        <router-link to="/courses" class="btn btn-primary">
          返回课程列表
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'

export default {
  name: 'CourseDetail',
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    
    const loading = ref(true)
    const enrolling = ref(false)
    const activeTab = ref('chapters')
    const showCreateDiscussion = ref(false)
    
    const course = computed(() => store.getters['course/currentCourse'])
    const chapters = ref([])
    const discussions = ref([])
    const relatedCourses = ref([])
    
    const courseId = computed(() => route.params.id)
    
    const getLevelText = (level) => {
      const levelMap = {
        'BEGINNER': '初级',
        'INTERMEDIATE': '中级',
        'ADVANCED': '高级'
      }
      return levelMap[level] || level
    }
    
    const getContentTypeText = (type) => {
      const typeMap = {
        'VIDEO': '视频',
        'DOCUMENT': '文档',
        'QUIZ': '测验'
      }
      return typeMap[type] || type
    }
    
    const formatTime = (timestamp) => {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
    }
    
    const fetchCourseDetail = async () => {
      loading.value = true
      try {
        await store.dispatch('course/fetchCourse', courseId.value)
        
        // 获取课程章节
        // chapters.value = await fetchChapters(courseId.value)
        
        // 获取课程讨论
        // discussions.value = await fetchDiscussions(courseId.value)
        
        // 获取相关课程推荐
        // relatedCourses.value = await fetchRelatedCourses(courseId.value)
        
      } catch (error) {
        console.error('获取课程详情失败:', error)
        store.dispatch('setError', '获取课程详情失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
    
    const handleEnroll = async () => {
      const isAuthenticated = store.getters['auth/isAuthenticated']
      if (!isAuthenticated) {
        router.push('/login')
        return
      }
      
      enrolling.value = true
      try {
        await store.dispatch('course/enrollCourse', courseId.value)
        // 报名成功，跳转到学习页面
        router.push(`/learning/course/${courseId.value}`)
      } catch (error) {
        console.error('报名失败:', error)
        store.dispatch('setError', error.response?.data?.message || '报名失败，请稍后重试')
      } finally {
        enrolling.value = false
      }
    }
    
    const goToCourse = (id) => {
      router.push(`/course/${id}`)
    }
    
    const goToDiscussion = (discussionId) => {
      router.push(`/discussion/${courseId.value}?discussionId=${discussionId}`)
    }
    
    onMounted(() => {
      fetchCourseDetail()
    })
    
    return {
      loading,
      enrolling,
      activeTab,
      showCreateDiscussion,
      course,
      chapters,
      discussions,
      relatedCourses,
      getLevelText,
      getContentTypeText,
      formatTime,
      handleEnroll,
      goToCourse,
      goToDiscussion
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.loading {
  text-align: center;
  padding: 60px 0;
  font-size: 18px;
  color: #7f8c8d;
}

.breadcrumb {
  margin-bottom: 20px;
  color: #7f8c8d;
  font-size: 14px;
}

.breadcrumb a {
  color: #3498db;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.course-header {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.course-title {
  color: #2c3e50;
  font-size: 32px;
  margin-bottom: 15px;
  line-height: 1.3;
}

.course-description {
  color: #7f8c8d;
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 25px;
}

.course-meta {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 30px;
}

.meta-item {
  display: flex;
  align-items: center;
}

.meta-label {
  color: #7f8c8d;
  margin-right: 8px;
  font-weight: 500;
}

.meta-value {
  color: #2c3e50;
  font-weight: 600;
}

.course-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.free-price {
  color: #27ae60;
  font-size: 24px;
  font-weight: bold;
}

.price {
  color: #e74c3c;
  font-size: 24px;
  font-weight: bold;
}

.btn-large {
  padding: 15px 30px;
  font-size: 18px;
}

.course-image {
  display: flex;
  align-items: center;
}

.course-image img {
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.course-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  overflow: hidden;
}

.content-tabs {
  display: flex;
  border-bottom: 1px solid #e1e8ed;
}

.tab-btn {
  flex: 1;
  padding: 20px;
  border: none;
  background: none;
  font-size: 16px;
  font-weight: 500;
  color: #7f8c8d;
  cursor: pointer;
  transition: all 0.3s;
}

.tab-btn.active {
  color: #3498db;
  border-bottom: 2px solid #3498db;
}

.tab-btn:hover {
  background-color: #f8f9fa;
}

.tab-content {
  padding: 30px;
}

.chapters-list {
  max-height: 600px;
  overflow-y: auto;
}

.chapter-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  margin-bottom: 15px;
  transition: all 0.3s;
}

.chapter-item:hover {
  border-color: #3498db;
  background-color: #f8f9fa;
}

.chapter-header {
  display: flex;
  align-items: center;
  flex: 1;
}

.chapter-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
}

.chapter-title {
  color: #2c3e50;
  margin-bottom: 5px;
  font-size: 16px;
}

.chapter-meta {
  display: flex;
  gap: 15px;
  font-size: 14px;
  color: #7f8c8d;
}

.chapter-type {
  background-color: #ecf0f1;
  padding: 2px 8px;
  border-radius: 4px;
}

.discussion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.discussion-header h3 {
  color: #2c3e50;
  margin: 0;
}

.discussion-item {
  padding: 20px;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s;
}

.discussion-item:hover {
  border-color: #3498db;
  background-color: #f8f9fa;
}

.discussion-title {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 16px;
}

.discussion-preview {
  color: #7f8c8d;
  margin-bottom: 15px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.discussion-meta {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #7f8c8d;
}

.related-courses h3 {
  color: #2c3e50;
  margin-bottom: 25px;
}

.related-courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.related-course-card {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
}

.related-course-card:hover {
  border-color: #3498db;
  transform: translateY(-2px);
}

.related-course-card img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.related-course-info {
  padding: 15px;
}

.related-course-info h4 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 16px;
}

.related-course-info p {
  color: #7f8c8d;
  font-size: 14px;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-course-meta {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.related-course-meta .price {
  color: #e74c3c;
  font-weight: 600;
}

.related-course-meta .students {
  color: #7f8c8d;
}

.empty-chapters,
.empty-discussions {
  text-align: center;
  padding: 40px 0;
  color: #7f8c8d;
}

.error-state {
  text-align: center;
  padding: 60px 0;
}

.error-state h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.error-state p {
  color: #7f8c8d;
  margin-bottom: 25px;
}

@media (max-width: 768px) {
  .course-header {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .course-meta {
    grid-template-columns: 1fr;
  }
  
  .course-actions {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .content-tabs {
    flex-direction: column;
  }
  
  .chapter-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .discussion-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .related-courses-grid {
    grid-template-columns: 1fr;
  }
}
</style>