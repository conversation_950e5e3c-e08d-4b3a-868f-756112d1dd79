import api from './index'

export default {
  // 获取课程讨论列表
  getDiscussions(courseId) {
    return api.get(`/api/discussion/${courseId}`)
  },
  
  // 获取讨论详情
  getDiscussion(discussionId) {
    return api.get(`/api/discussion/detail/${discussionId}`)
  },
  
  // 获取讨论回复
  getReplies(discussionId) {
    return api.get(`/api/discussion/${discussionId}/replies`)
  },
  
  // 创建讨论
  createDiscussion(discussionData) {
    return api.post('/api/discussion', discussionData)
  },
  
  // 创建回复
  createReply(replyData) {
    return api.post(`/api/discussion/${replyData.discussionId}/reply`, replyData)
  },
  
  // 点赞讨论
  likeDiscussion(discussionId) {
    return api.put(`/api/discussion/${discussionId}/like`)
  },
  
  // 搜索讨论
  searchDiscussions(courseId, keyword) {
    return api.get('/api/discussion/search', {
      params: { courseId, keyword }
    })
  }
}