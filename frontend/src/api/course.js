import api from './index'

export default {
  // 获取课程列表
  getCourses(params = {}) {
    return api.get('/api/course/list', { params })
  },
  
  // 获取课程详情
  getCourse(courseId) {
    return api.get(`/api/course/${courseId}`)
  },
  
  // 创建课程
  createCourse(courseData) {
    return api.post('/api/course', courseData)
  },
  
  // 更新课程
  updateCourse(courseId, courseData) {
    return api.put(`/api/course/${courseId}`, courseData)
  },
  
  // 删除课程
  deleteCourse(courseId) {
    return api.delete(`/api/course/${courseId}`)
  },
  
  // 搜索课程
  searchCourses(searchParams) {
    return api.get('/api/course/search', { params: searchParams })
  },
  
  // 报名课程
  enrollCourse(courseId) {
    return api.post(`/api/course/${courseId}/enroll`)
  },
  
  // 获取课程分类
  getCategories() {
    return api.get('/api/course/categories')
  },
  
  // 获取课程章节
  getCourseChapters(courseId) {
    return api.get(`/api/course/${courseId}/chapters`)
  }
}