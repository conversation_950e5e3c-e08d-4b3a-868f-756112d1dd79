import api from './index'

export default {
  // 用户登录
  login(credentials) {
    return api.post('/api/user/login', credentials)
  },
  
  // 用户注册
  register(userData) {
    return api.post('/api/user/register', userData)
  },
  
  // 获取用户信息
  getProfile() {
    return api.get('/api/user/profile')
  },
  
  // 更新用户信息
  updateProfile(profileData) {
    return api.put('/api/user/profile', profileData)
  },
  
  // 重置密码
  resetPassword(email) {
    return api.post('/api/user/reset-password', { email })
  },
  
  // 邮箱验证
  verifyEmail(token) {
    return api.get(`/api/user/verify-email?token=${token}`)
  }
}