/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/bin/java  -server -Xms512m -Xmx512m -Xmn256m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=320m -Xms512m -Xmx512m -Xmn256m -Dnacos.standalone=true -Dnacos.member.list= -Xlog:gc*:file=/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/logs/nacos_gc.log:time,tags:filecount=10,filesize=100m -Dloader.path=/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/plugins,/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/plugins/health,/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/plugins/cmdb,/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/plugins/selector -Dnacos.home=/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos -jar /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/target/nacos-server.jar  --spring.config.additional-location=file:/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/conf/ --logging.config=/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/conf/nacos-logback.xml --server.max-http-header-size=524288

         ,--.
       ,--.'|
   ,--,:  : |                                           Nacos 2.3.0
,`--.'`|  ' :                       ,---.               Running in stand alone mode, All function modules
|   :  :  | |                      '   ,'\   .--.--.    Port: 8848
:   |   \ | :  ,--.--.     ,---.  /   /   | /  /    '   Pid: 87070
|   : '  '; | /       \   /     \.   ; ,. :|  :  /`./   Console: http://192.168.0.110:8848/nacos/index.html
'   ' ;.    ;.--.  .-. | /    / ''   | |: :|  :  ;_
|   | | \   | \__\/: . ..    ' / '   | .; : \  \    `.      https://nacos.io
'   : |  ; .' ," .--.; |'   ; :__|   :    |  `----.   \
|   | '`--'  /  /  ,.  |'   | '.'|\   \  /  /  /`--'  /
'   : |     ;  :   .'   \   :    : `----'  '--'.     /
;   |.'     |  ,     .-./\   \  /            `--'---'
'---'        `--`---'     `----'

2025-08-01 15:06:19,732 INFO Tomcat initialized with port(s): 8848 (http)

2025-08-01 15:06:19,876 INFO Root WebApplicationContext: initialization completed in 1243 ms

2025-08-01 15:06:21,523 INFO Adding welcome page: class path resource [static/index.html]

2025-08-01 15:06:21,695 WARN You are asking Spring Security to ignore Ant [pattern='/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-08-01 15:06:21,695 INFO Will not secure Ant [pattern='/**']

2025-08-01 15:06:21,703 INFO Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@bb25753, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@ee21292, org.springframework.security.web.context.SecurityContextPersistenceFilter@77b3752b, org.springframework.security.web.header.HeaderWriterFilter@519c6fcc, org.springframework.security.web.csrf.CsrfFilter@6d6ac396, org.springframework.security.web.authentication.logout.LogoutFilter@445821a6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@319642db, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6b321262, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@40c06358, org.springframework.security.web.session.SessionManagementFilter@62aeddc8, org.springframework.security.web.access.ExceptionTranslationFilter@432af457]

2025-08-01 15:06:21,715 INFO Exposing 1 endpoint(s) beneath base path '/actuator'

2025-08-01 15:06:21,731 INFO Tomcat started on port(s): 8848 (http) with context path '/nacos'

2025-08-01 15:06:21,741 INFO Nacos started successfully in stand alone mode. use external storage

2025-08-01 15:06:35,093 INFO Initializing Servlet 'dispatcherServlet'

2025-08-01 15:06:35,094 INFO Completed initialization in 1 ms

