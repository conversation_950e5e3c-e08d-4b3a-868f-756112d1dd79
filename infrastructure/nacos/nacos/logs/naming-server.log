2025-08-01 15:06:20,763 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.CmdbSelectorContextBuilder) contextType(CMDB) successfully.

2025-08-01 15:06:20,763 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.NoneSelectorContextBuilder) contextType(NONE) successfully.

2025-08-01 15:06:20,763 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.LabelSelector) type(label) contextType(CMDB) successfully.

2025-08-01 15:06:20,763 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.NoneSelector) type(none) contextType(NONE) successfully.

2025-08-01 15:06:21,352 INFO Load instance extension handler []

2025-08-01 15:20:24,842 INFO Client connection 1754032824785_127.0.0.1_53787 connect

2025-08-01 15:20:24,947 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=0}, 1754032824785_127.0.0.1_53787

2025-08-01 15:45:37,946 INFO Client connection 1754034337917_127.0.0.1_58255 connect

2025-08-01 15:45:38,054 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=0}, 1754034337917_127.0.0.1_58255

2025-08-01 15:53:29,197 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, 1754034337917_127.0.0.1_58255

2025-08-01 15:53:29,204 INFO Client connection 1754034337917_127.0.0.1_58255 disconnect, remove instances and subscribers

2025-08-01 15:53:47,408 INFO Client connection 1754034827382_127.0.0.1_59137 connect

2025-08-01 15:53:47,522 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=2}, 1754034827382_127.0.0.1_59137

2025-08-01 16:06:04,336 INFO Client connection 1754035564309_127.0.0.1_61201 connect

2025-08-01 16:06:04,445 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=0}, 1754035564309_127.0.0.1_61201

2025-08-01 16:10:43,206 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=1}, 1754035564309_127.0.0.1_61201

2025-08-01 16:10:43,210 INFO Client connection 1754035564309_127.0.0.1_61201 disconnect, remove instances and subscribers

2025-08-01 16:11:31,575 INFO Client connection 1754035891547_127.0.0.1_61635 connect

2025-08-01 16:11:31,684 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=2}, 1754035891547_127.0.0.1_61635

2025-08-01 16:12:14,873 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=3}, 1754035891547_127.0.0.1_61635

2025-08-01 16:12:14,877 INFO Client connection 1754035891547_127.0.0.1_61635 disconnect, remove instances and subscribers

2025-08-01 16:12:55,073 INFO Client connection 1754035975042_127.0.0.1_61800 connect

2025-08-01 16:12:55,182 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=4}, 1754035975042_127.0.0.1_61800

2025-08-01 16:31:36,971 INFO Client connection 1754037096943_127.0.0.1_64539 connect

2025-08-01 16:31:37,081 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=0}, 1754037096943_127.0.0.1_64539

2025-08-01 16:33:13,295 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=1}, 1754037096943_127.0.0.1_64539

2025-08-01 16:33:13,302 INFO Client connection 1754037096943_127.0.0.1_64539 disconnect, remove instances and subscribers

2025-08-01 16:33:22,177 INFO Client connection 1754037202150_127.0.0.1_64943 connect

2025-08-01 16:33:22,289 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=2}, 1754037202150_127.0.0.1_64943

2025-08-01 16:37:39,594 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=3}, 1754037202150_127.0.0.1_64943

2025-08-01 16:37:39,597 INFO Client connection 1754037202150_127.0.0.1_64943 disconnect, remove instances and subscribers

2025-08-01 16:37:51,315 INFO Client connection 1754037471288_127.0.0.1_65519 connect

2025-08-01 16:37:51,427 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=4}, 1754037471288_127.0.0.1_65519

2025-08-01 16:50:16,986 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=5}, 1754037471288_127.0.0.1_65519

2025-08-01 16:50:16,990 INFO Client connection 1754037471288_127.0.0.1_65519 disconnect, remove instances and subscribers

2025-08-01 16:50:28,145 INFO Client connection 1754038228119_127.0.0.1_50467 connect

2025-08-01 16:50:28,257 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=6}, 1754038228119_127.0.0.1_50467

2025-08-01 17:28:29,796 INFO Client connection 1754040509771_127.0.0.1_55028 connect

2025-08-01 17:28:29,907 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=0}, 1754040509771_127.0.0.1_55028

2025-08-01 17:28:44,574 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=1}, 1754040509771_127.0.0.1_55028

2025-08-01 17:28:44,577 INFO Client connection 1754040509771_127.0.0.1_55028 disconnect, remove instances and subscribers

2025-08-01 17:29:13,818 INFO Client connection 1754040553789_127.0.0.1_55130 connect

2025-08-01 17:29:13,928 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=2}, 1754040553789_127.0.0.1_55130

2025-08-01 17:30:04,368 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=3}, 1754040553789_127.0.0.1_55130

2025-08-01 17:30:04,372 INFO Client connection 1754040553789_127.0.0.1_55130 disconnect, remove instances and subscribers

2025-08-01 17:31:03,965 INFO Client connection 1754040663940_127.0.0.1_55421 connect

2025-08-01 17:31:04,075 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=4}, 1754040663940_127.0.0.1_55421

2025-08-01 17:31:59,939 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=5}, 1754040663940_127.0.0.1_55421

2025-08-01 17:31:59,943 INFO Client connection 1754040663940_127.0.0.1_55421 disconnect, remove instances and subscribers

2025-08-01 17:32:14,286 INFO Client connection 1754040734260_127.0.0.1_55629 connect

2025-08-01 17:32:14,396 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=6}, 1754040734260_127.0.0.1_55629

2025-08-01 17:32:51,105 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=7}, 1754040734260_127.0.0.1_55629

2025-08-01 17:32:51,110 INFO Client connection 1754040734260_127.0.0.1_55629 disconnect, remove instances and subscribers

2025-08-01 17:33:00,637 INFO Client connection 1754040780608_127.0.0.1_55750 connect

2025-08-01 17:33:00,746 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=8}, 1754040780608_127.0.0.1_55750

2025-08-01 17:33:31,751 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=9}, 1754040780608_127.0.0.1_55750

2025-08-01 17:33:31,755 INFO Client connection 1754040780608_127.0.0.1_55750 disconnect, remove instances and subscribers

2025-08-01 17:33:41,828 INFO Client connection 1754040821800_127.0.0.1_55922 connect

2025-08-01 17:33:41,938 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=10}, 1754040821800_127.0.0.1_55922

2025-08-01 17:34:18,041 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, 1754040821800_127.0.0.1_55922

2025-08-01 17:34:18,044 INFO Client connection 1754040821800_127.0.0.1_55922 disconnect, remove instances and subscribers

2025-08-01 17:35:51,727 WARN namespace : public, [DEFAULT_GROUP@@discussion-service] services are automatically cleaned

2025-08-01 17:36:16,809 INFO Client connection 1754040976781_127.0.0.1_56281 connect

2025-08-01 17:36:16,920 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=0}, 1754040976781_127.0.0.1_56281

2025-08-01 17:38:53,857 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=1}, 1754040976781_127.0.0.1_56281

2025-08-01 17:38:53,860 INFO Client connection 1754040976781_127.0.0.1_56281 disconnect, remove instances and subscribers

2025-08-01 17:38:53,861 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}, 1754032824785_127.0.0.1_53787

2025-08-01 17:38:53,864 INFO Client connection 1754032824785_127.0.0.1_53787 disconnect, remove instances and subscribers

2025-08-01 17:40:51,750 WARN namespace : public, [DEFAULT_GROUP@@discussion-service] services are automatically cleaned

2025-08-01 17:40:51,750 WARN namespace : public, [DEFAULT_GROUP@@gateway-service] services are automatically cleaned

2025-08-01 17:45:47,138 INFO Client connection 1754041547112_127.0.0.1_57392 connect

2025-08-01 17:45:47,249 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=0}, 1754041547112_127.0.0.1_57392

2025-08-01 17:49:04,488 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=1}, 1754041547112_127.0.0.1_57392

2025-08-01 17:49:04,491 INFO Client connection 1754041547112_127.0.0.1_57392 disconnect, remove instances and subscribers

2025-08-01 17:49:16,195 INFO Client connection 1754041756169_127.0.0.1_57809 connect

2025-08-01 17:49:16,301 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=2}, 1754041756169_127.0.0.1_57809

2025-08-01 17:51:12,824 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=3}, 1754041756169_127.0.0.1_57809

2025-08-01 17:51:12,831 INFO Client connection 1754041756169_127.0.0.1_57809 disconnect, remove instances and subscribers

2025-08-01 17:51:20,906 INFO Client connection 1754041880879_127.0.0.1_58005 connect

2025-08-01 17:51:21,017 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=4}, 1754041880879_127.0.0.1_58005

2025-08-01 17:52:52,270 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=5}, 1754041880879_127.0.0.1_58005

2025-08-01 17:52:52,274 INFO Client connection 1754041880879_127.0.0.1_58005 disconnect, remove instances and subscribers

2025-08-01 17:53:05,200 INFO Client connection 1754041985175_127.0.0.1_58376 connect

2025-08-01 17:53:05,313 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=6}, 1754041985175_127.0.0.1_58376

2025-08-01 17:58:57,957 INFO Client connection 1754042337930_127.0.0.1_59095 connect

2025-08-01 17:58:58,069 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=0}, 1754042337930_127.0.0.1_59095

2025-08-01 17:59:50,095 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=1}, 1754042337930_127.0.0.1_59095

2025-08-01 17:59:50,099 INFO Client connection 1754042337930_127.0.0.1_59095 disconnect, remove instances and subscribers

2025-08-01 18:00:02,226 INFO Client connection 1754042402201_127.0.0.1_59286 connect

2025-08-01 18:00:02,336 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=2}, 1754042402201_127.0.0.1_59286

2025-08-01 18:01:51,373 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=7}, 1754041985175_127.0.0.1_58376

2025-08-01 18:01:51,380 INFO Client connection 1754041985175_127.0.0.1_58376 disconnect, remove instances and subscribers

2025-08-01 18:02:04,072 INFO Client connection 1754042524046_127.0.0.1_59659 connect

2025-08-01 18:02:04,185 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=8}, 1754042524046_127.0.0.1_59659

2025-08-01 18:27:35,856 INFO Client connection 1754044055817_127.0.0.1_64395 connect

2025-08-01 18:27:35,965 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=0}, 1754044055817_127.0.0.1_64395

2025-08-01 18:31:13,632 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}, 1754044055817_127.0.0.1_64395

2025-08-01 18:31:13,642 INFO Client connection 1754044055817_127.0.0.1_64395 disconnect, remove instances and subscribers

2025-08-01 18:31:26,448 INFO Client connection 1754044286410_127.0.0.1_49392 connect

2025-08-01 18:31:26,560 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=2}, 1754044286410_127.0.0.1_49392

2025-08-01 18:34:10,166 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=3}, 1754044286410_127.0.0.1_49392

2025-08-01 18:34:10,170 INFO Client connection 1754044286410_127.0.0.1_49392 disconnect, remove instances and subscribers

2025-08-01 18:34:26,549 INFO Client connection 1754044466511_127.0.0.1_50616 connect

2025-08-01 18:34:26,664 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=4}, 1754044466511_127.0.0.1_50616

2025-08-01 18:35:44,745 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=5}, 1754044466511_127.0.0.1_50616

2025-08-01 18:35:44,749 INFO Client connection 1754044466511_127.0.0.1_50616 disconnect, remove instances and subscribers

2025-08-01 18:35:54,142 INFO Client connection 1754044554103_127.0.0.1_51169 connect

2025-08-01 18:35:54,257 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=6}, 1754044554103_127.0.0.1_51169

2025-08-01 18:45:02,550 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, 1754034827382_127.0.0.1_59137

2025-08-01 18:45:02,556 INFO Client connection 1754034827382_127.0.0.1_59137 disconnect, remove instances and subscribers

2025-08-01 18:45:07,470 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=3}, 1754042402201_127.0.0.1_59286

2025-08-01 18:45:07,494 INFO Client connection 1754042402201_127.0.0.1_59286 disconnect, remove instances and subscribers

2025-08-01 18:45:12,619 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=5}, 1754035975042_127.0.0.1_61800

2025-08-01 18:45:12,624 INFO Client connection 1754035975042_127.0.0.1_61800 disconnect, remove instances and subscribers

2025-08-01 18:45:17,305 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=7}, 1754038228119_127.0.0.1_50467

2025-08-01 18:45:17,311 INFO Client connection 1754038228119_127.0.0.1_50467 disconnect, remove instances and subscribers

2025-08-01 18:45:22,008 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=9}, 1754042524046_127.0.0.1_59659

2025-08-01 18:45:22,011 INFO Client connection 1754042524046_127.0.0.1_59659 disconnect, remove instances and subscribers

2025-08-01 18:45:28,901 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=7}, 1754044554103_127.0.0.1_51169

2025-08-01 18:45:28,907 INFO Client connection 1754044554103_127.0.0.1_51169 disconnect, remove instances and subscribers

2025-08-01 18:46:02,859 INFO Client connection 1754045162818_127.0.0.1_54493 connect

2025-08-01 18:46:02,971 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=8}, 1754045162818_127.0.0.1_54493

2025-08-01 18:46:07,832 INFO Client connection 1754045167803_127.0.0.1_54551 connect

2025-08-01 18:46:07,947 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=4}, 1754045167803_127.0.0.1_54551

2025-08-01 18:46:11,988 INFO Client connection 1754045171960_127.0.0.1_54573 connect

2025-08-01 18:46:12,099 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=4}, 1754045171960_127.0.0.1_54573

2025-08-01 18:46:18,607 INFO Client connection 1754045178577_127.0.0.1_54634 connect

2025-08-01 18:46:18,721 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=6}, 1754045178577_127.0.0.1_54634

2025-08-01 18:46:23,159 INFO Client connection 1754045183130_127.0.0.1_54661 connect

2025-08-01 18:46:23,272 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=8}, 1754045183130_127.0.0.1_54661

2025-08-01 18:46:29,836 INFO Client connection 1754045189810_127.0.0.1_54715 connect

2025-08-01 18:46:29,943 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=10}, 1754045189810_127.0.0.1_54715

2025-08-01 18:52:39,056 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=5}, 1754045171960_127.0.0.1_54573

2025-08-01 18:52:39,060 INFO Client connection 1754045171960_127.0.0.1_54573 disconnect, remove instances and subscribers

2025-08-01 18:53:51,980 WARN namespace : public, [DEFAULT_GROUP@@course-service] services are automatically cleaned

2025-08-01 18:57:29,887 INFO Client connection 1754045849857_127.0.0.1_58617 connect

2025-08-01 18:57:29,996 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=0}, 1754045849857_127.0.0.1_58617

2025-08-01 18:58:28,888 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, 1754045849857_127.0.0.1_58617

2025-08-01 18:58:28,891 INFO Client connection 1754045849857_127.0.0.1_58617 disconnect, remove instances and subscribers

2025-08-01 18:58:49,678 INFO Client connection 1754045929650_127.0.0.1_58952 connect

2025-08-01 18:58:49,803 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=2}, 1754045929650_127.0.0.1_58952

2025-08-01 18:59:46,878 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, 1754045929650_127.0.0.1_58952

2025-08-01 18:59:46,882 INFO Client connection 1754045929650_127.0.0.1_58952 disconnect, remove instances and subscribers

2025-08-01 19:00:52,006 WARN namespace : public, [DEFAULT_GROUP@@course-service] services are automatically cleaned

