[2025-08-01T15:06:18.037+0800][gc,init] CardTable entry size: 512
[2025-08-01T15:06:18.037+0800][gc     ] Using G1
[2025-08-01T15:06:18.038+0800][gc,init] Version: 23.0.2 (release)
[2025-08-01T15:06:18.038+0800][gc,init] CPUs: 12 total, 12 available
[2025-08-01T15:06:18.038+0800][gc,init] Memory: 49152M
[2025-08-01T15:06:18.038+0800][gc,init] Large Page Support: Disabled
[2025-08-01T15:06:18.038+0800][gc,init] NUMA Support: Disabled
[2025-08-01T15:06:18.038+0800][gc,init] Compressed Oops: Enabled (Zero based)
[2025-08-01T15:06:18.038+0800][gc,init] Heap Region Size: 1M
[2025-08-01T15:06:18.038+0800][gc,init] Heap Min Capacity: 512M
[2025-08-01T15:06:18.038+0800][gc,init] Heap Initial Capacity: 512M
[2025-08-01T15:06:18.038+0800][gc,init] Heap Max Capacity: 512M
[2025-08-01T15:06:18.038+0800][gc,init] Pre-touch: Disabled
[2025-08-01T15:06:18.038+0800][gc,init] Parallel Workers: 10
[2025-08-01T15:06:18.038+0800][gc,init] Concurrent Workers: 3
[2025-08-01T15:06:18.038+0800][gc,init] Concurrent Refinement Workers: 10
[2025-08-01T15:06:18.038+0800][gc,init] Periodic GC: Disabled
[2025-08-01T15:06:18.041+0800][gc,metaspace] CDS archive(s) mapped at: [0x00007c0000000000-0x00007c0000d94000-0x00007c0000d94000), size 14237696, SharedBaseAddress: 0x00007c0000000000, ArchiveRelocationMode: 1.
[2025-08-01T15:06:18.041+0800][gc,metaspace] Compressed class space mapped at: 0x00007c0001000000-0x00007c0011000000, reserved size: 268435456
[2025-08-01T15:06:18.041+0800][gc,metaspace] Narrow klass base: 0x00007c0000000000, Narrow klass shift: 0, Narrow klass range: 0x100000000
[2025-08-01T15:06:18.468+0800][gc,start    ] GC(0) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:06:18.468+0800][gc,task     ] GC(0) Using 10 workers of 10 for evacuation
[2025-08-01T15:06:18.470+0800][gc,phases   ] GC(0)   Pre Evacuate Collection Set: 0.09ms
[2025-08-01T15:06:18.470+0800][gc,phases   ] GC(0)   Merge Heap Roots: 0.07ms
[2025-08-01T15:06:18.470+0800][gc,phases   ] GC(0)   Evacuate Collection Set: 1.51ms
[2025-08-01T15:06:18.470+0800][gc,phases   ] GC(0)   Post Evacuate Collection Set: 0.29ms
[2025-08-01T15:06:18.470+0800][gc,phases   ] GC(0)   Other: 0.21ms
[2025-08-01T15:06:18.470+0800][gc,heap     ] GC(0) Eden regions: 256->0(250)
[2025-08-01T15:06:18.470+0800][gc,heap     ] GC(0) Survivor regions: 0->6(32)
[2025-08-01T15:06:18.470+0800][gc,heap     ] GC(0) Old regions: 2->2
[2025-08-01T15:06:18.470+0800][gc,heap     ] GC(0) Humongous regions: 3->0
[2025-08-01T15:06:18.470+0800][gc,metaspace] GC(0) Metaspace: 10403K(10624K)->10403K(10624K) NonClass: 9146K(9280K)->9146K(9280K) Class: 1257K(1344K)->1257K(1344K)
[2025-08-01T15:06:18.470+0800][gc          ] GC(0) Pause Young (Normal) (G1 Evacuation Pause) 260M->6M(512M) 2.312ms
[2025-08-01T15:06:18.470+0800][gc,cpu      ] GC(0) User=0.01s Sys=0.00s Real=0.01s
[2025-08-01T15:06:18.636+0800][gc,start    ] GC(1) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:06:18.636+0800][gc,task     ] GC(1) Using 10 workers of 10 for evacuation
[2025-08-01T15:06:18.647+0800][gc,phases   ] GC(1)   Pre Evacuate Collection Set: 0.10ms
[2025-08-01T15:06:18.647+0800][gc,phases   ] GC(1)   Merge Heap Roots: 0.08ms
[2025-08-01T15:06:18.647+0800][gc,phases   ] GC(1)   Evacuate Collection Set: 10.76ms
[2025-08-01T15:06:18.647+0800][gc,phases   ] GC(1)   Post Evacuate Collection Set: 0.37ms
[2025-08-01T15:06:18.647+0800][gc,phases   ] GC(1)   Other: 0.04ms
[2025-08-01T15:06:18.647+0800][gc,heap     ] GC(1) Eden regions: 250->0(248)
[2025-08-01T15:06:18.647+0800][gc,heap     ] GC(1) Survivor regions: 6->8(32)
[2025-08-01T15:06:18.647+0800][gc,heap     ] GC(1) Old regions: 2->2
[2025-08-01T15:06:18.648+0800][gc,heap     ] GC(1) Humongous regions: 0->0
[2025-08-01T15:06:18.648+0800][gc,metaspace] GC(1) Metaspace: 17113K(17408K)->17113K(17408K) NonClass: 15056K(15232K)->15056K(15232K) Class: 2056K(2176K)->2056K(2176K)
[2025-08-01T15:06:18.648+0800][gc          ] GC(1) Pause Young (Normal) (G1 Evacuation Pause) 256M->8M(512M) 11.487ms
[2025-08-01T15:06:18.648+0800][gc,cpu      ] GC(1) User=0.02s Sys=0.01s Real=0.01s
[2025-08-01T15:06:18.871+0800][gc,start    ] GC(2) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:06:18.871+0800][gc,task     ] GC(2) Using 10 workers of 10 for evacuation
[2025-08-01T15:06:18.874+0800][gc,phases   ] GC(2)   Pre Evacuate Collection Set: 0.12ms
[2025-08-01T15:06:18.874+0800][gc,phases   ] GC(2)   Merge Heap Roots: 0.08ms
[2025-08-01T15:06:18.874+0800][gc,phases   ] GC(2)   Evacuate Collection Set: 2.29ms
[2025-08-01T15:06:18.874+0800][gc,phases   ] GC(2)   Post Evacuate Collection Set: 0.35ms
[2025-08-01T15:06:18.874+0800][gc,phases   ] GC(2)   Other: 0.04ms
[2025-08-01T15:06:18.874+0800][gc,heap     ] GC(2) Eden regions: 248->0(239)
[2025-08-01T15:06:18.874+0800][gc,heap     ] GC(2) Survivor regions: 8->17(32)
[2025-08-01T15:06:18.874+0800][gc,heap     ] GC(2) Old regions: 2->2
[2025-08-01T15:06:18.874+0800][gc,heap     ] GC(2) Humongous regions: 0->0
[2025-08-01T15:06:18.874+0800][gc,metaspace] GC(2) Metaspace: 19100K(19392K)->19100K(19392K) NonClass: 16791K(16960K)->16791K(16960K) Class: 2309K(2432K)->2309K(2432K)
[2025-08-01T15:06:18.874+0800][gc          ] GC(2) Pause Young (Normal) (G1 Evacuation Pause) 256M->17M(512M) 2.995ms
[2025-08-01T15:06:18.874+0800][gc,cpu      ] GC(2) User=0.02s Sys=0.01s Real=0.00s
[2025-08-01T15:06:19.004+0800][gc,start    ] GC(3) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:06:19.004+0800][gc,task     ] GC(3) Using 10 workers of 10 for evacuation
[2025-08-01T15:06:19.007+0800][gc,phases   ] GC(3)   Pre Evacuate Collection Set: 0.12ms
[2025-08-01T15:06:19.007+0800][gc,phases   ] GC(3)   Merge Heap Roots: 0.07ms
[2025-08-01T15:06:19.007+0800][gc,phases   ] GC(3)   Evacuate Collection Set: 2.06ms
[2025-08-01T15:06:19.007+0800][gc,phases   ] GC(3)   Post Evacuate Collection Set: 0.37ms
[2025-08-01T15:06:19.007+0800][gc,phases   ] GC(3)   Other: 0.04ms
[2025-08-01T15:06:19.007+0800][gc,heap     ] GC(3) Eden regions: 239->0(236)
[2025-08-01T15:06:19.007+0800][gc,heap     ] GC(3) Survivor regions: 17->20(32)
[2025-08-01T15:06:19.007+0800][gc,heap     ] GC(3) Old regions: 2->2
[2025-08-01T15:06:19.007+0800][gc,heap     ] GC(3) Humongous regions: 0->0
[2025-08-01T15:06:19.007+0800][gc,metaspace] GC(3) Metaspace: 20228K(20480K)->20228K(20480K) NonClass: 17797K(17920K)->17797K(17920K) Class: 2430K(2560K)->2430K(2560K)
[2025-08-01T15:06:19.007+0800][gc          ] GC(3) Pause Young (Normal) (G1 Evacuation Pause) 256M->20M(512M) 2.775ms
[2025-08-01T15:06:19.007+0800][gc,cpu      ] GC(3) User=0.03s Sys=0.01s Real=0.00s
[2025-08-01T15:06:19.155+0800][gc,start    ] GC(4) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:06:19.155+0800][gc,task     ] GC(4) Using 10 workers of 10 for evacuation
[2025-08-01T15:06:19.158+0800][gc,phases   ] GC(4)   Pre Evacuate Collection Set: 0.09ms
[2025-08-01T15:06:19.158+0800][gc,phases   ] GC(4)   Merge Heap Roots: 0.10ms
[2025-08-01T15:06:19.158+0800][gc,phases   ] GC(4)   Evacuate Collection Set: 2.42ms
[2025-08-01T15:06:19.158+0800][gc,phases   ] GC(4)   Post Evacuate Collection Set: 0.58ms
[2025-08-01T15:06:19.158+0800][gc,phases   ] GC(4)   Other: 0.04ms
[2025-08-01T15:06:19.158+0800][gc,heap     ] GC(4) Eden regions: 236->0(239)
[2025-08-01T15:06:19.158+0800][gc,heap     ] GC(4) Survivor regions: 20->17(32)
[2025-08-01T15:06:19.158+0800][gc,heap     ] GC(4) Old regions: 2->7
[2025-08-01T15:06:19.158+0800][gc,heap     ] GC(4) Humongous regions: 0->0
[2025-08-01T15:06:19.158+0800][gc,metaspace] GC(4) Metaspace: 22436K(22720K)->22436K(22720K) NonClass: 19758K(19904K)->19758K(19904K) Class: 2677K(2816K)->2677K(2816K)
[2025-08-01T15:06:19.158+0800][gc          ] GC(4) Pause Young (Normal) (G1 Evacuation Pause) 256M->22M(512M) 3.350ms
[2025-08-01T15:06:19.158+0800][gc,cpu      ] GC(4) User=0.02s Sys=0.01s Real=0.00s
[2025-08-01T15:06:19.370+0800][gc,start    ] GC(5) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:06:19.370+0800][gc,task     ] GC(5) Using 10 workers of 10 for evacuation
[2025-08-01T15:06:19.372+0800][gc,phases   ] GC(5)   Pre Evacuate Collection Set: 0.12ms
[2025-08-01T15:06:19.372+0800][gc,phases   ] GC(5)   Merge Heap Roots: 0.08ms
[2025-08-01T15:06:19.372+0800][gc,phases   ] GC(5)   Evacuate Collection Set: 1.46ms
[2025-08-01T15:06:19.372+0800][gc,phases   ] GC(5)   Post Evacuate Collection Set: 0.25ms
[2025-08-01T15:06:19.372+0800][gc,phases   ] GC(5)   Other: 0.05ms
[2025-08-01T15:06:19.372+0800][gc,heap     ] GC(5) Eden regions: 239->0(239)
[2025-08-01T15:06:19.372+0800][gc,heap     ] GC(5) Survivor regions: 17->17(32)
[2025-08-01T15:06:19.372+0800][gc,heap     ] GC(5) Old regions: 7->7
[2025-08-01T15:06:19.372+0800][gc,heap     ] GC(5) Humongous regions: 0->0
[2025-08-01T15:06:19.372+0800][gc,metaspace] GC(5) Metaspace: 25637K(25920K)->25637K(25920K) NonClass: 22500K(22656K)->22500K(22656K) Class: 3137K(3264K)->3137K(3264K)
[2025-08-01T15:06:19.372+0800][gc          ] GC(5) Pause Young (Normal) (G1 Evacuation Pause) 261M->23M(512M) 2.078ms
[2025-08-01T15:06:19.372+0800][gc,cpu      ] GC(5) User=0.01s Sys=0.00s Real=0.00s
[2025-08-01T15:06:19.593+0800][gc,start    ] GC(6) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:06:19.593+0800][gc,task     ] GC(6) Using 10 workers of 10 for evacuation
[2025-08-01T15:06:19.595+0800][gc,phases   ] GC(6)   Pre Evacuate Collection Set: 0.08ms
[2025-08-01T15:06:19.595+0800][gc,phases   ] GC(6)   Merge Heap Roots: 0.07ms
[2025-08-01T15:06:19.595+0800][gc,phases   ] GC(6)   Evacuate Collection Set: 1.80ms
[2025-08-01T15:06:19.595+0800][gc,phases   ] GC(6)   Post Evacuate Collection Set: 0.27ms
[2025-08-01T15:06:19.595+0800][gc,phases   ] GC(6)   Other: 0.05ms
[2025-08-01T15:06:19.595+0800][gc,heap     ] GC(6) Eden regions: 239->0(237)
[2025-08-01T15:06:19.595+0800][gc,heap     ] GC(6) Survivor regions: 17->19(32)
[2025-08-01T15:06:19.595+0800][gc,heap     ] GC(6) Old regions: 7->7
[2025-08-01T15:06:19.595+0800][gc,heap     ] GC(6) Humongous regions: 0->0
[2025-08-01T15:06:19.595+0800][gc,metaspace] GC(6) Metaspace: 29713K(30080K)->29713K(30080K) NonClass: 26056K(26240K)->26056K(26240K) Class: 3657K(3840K)->3657K(3840K)
[2025-08-01T15:06:19.595+0800][gc          ] GC(6) Pause Young (Normal) (G1 Evacuation Pause) 262M->24M(512M) 2.384ms
[2025-08-01T15:06:19.595+0800][gc,cpu      ] GC(6) User=0.02s Sys=0.00s Real=0.00s
[2025-08-01T15:06:19.993+0800][gc,start    ] GC(7) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:06:19.993+0800][gc,task     ] GC(7) Using 10 workers of 10 for evacuation
[2025-08-01T15:06:19.996+0800][gc,phases   ] GC(7)   Pre Evacuate Collection Set: 0.10ms
[2025-08-01T15:06:19.996+0800][gc,phases   ] GC(7)   Merge Heap Roots: 0.06ms
[2025-08-01T15:06:19.996+0800][gc,phases   ] GC(7)   Evacuate Collection Set: 3.09ms
[2025-08-01T15:06:19.996+0800][gc,phases   ] GC(7)   Post Evacuate Collection Set: 0.43ms
[2025-08-01T15:06:19.996+0800][gc,phases   ] GC(7)   Other: 0.04ms
[2025-08-01T15:06:19.996+0800][gc,heap     ] GC(7) Eden regions: 237->0(232)
[2025-08-01T15:06:19.996+0800][gc,heap     ] GC(7) Survivor regions: 19->24(32)
[2025-08-01T15:06:19.996+0800][gc,heap     ] GC(7) Old regions: 7->9
[2025-08-01T15:06:19.996+0800][gc,heap     ] GC(7) Humongous regions: 0->0
[2025-08-01T15:06:19.996+0800][gc,metaspace] GC(7) Metaspace: 39720K(40128K)->39720K(40128K) NonClass: 34844K(35072K)->34844K(35072K) Class: 4876K(5056K)->4876K(5056K)
[2025-08-01T15:06:19.996+0800][gc          ] GC(7) Pause Young (Normal) (G1 Evacuation Pause) 261M->31M(512M) 3.824ms
[2025-08-01T15:06:19.996+0800][gc,cpu      ] GC(7) User=0.02s Sys=0.00s Real=0.00s
[2025-08-01T15:06:20.334+0800][gc,start    ] GC(8) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:06:20.335+0800][gc,task     ] GC(8) Using 10 workers of 10 for evacuation
[2025-08-01T15:06:20.339+0800][gc,phases   ] GC(8)   Pre Evacuate Collection Set: 0.14ms
[2025-08-01T15:06:20.339+0800][gc,phases   ] GC(8)   Merge Heap Roots: 0.07ms
[2025-08-01T15:06:20.339+0800][gc,phases   ] GC(8)   Evacuate Collection Set: 3.65ms
[2025-08-01T15:06:20.339+0800][gc,phases   ] GC(8)   Post Evacuate Collection Set: 0.41ms
[2025-08-01T15:06:20.339+0800][gc,phases   ] GC(8)   Other: 0.04ms
[2025-08-01T15:06:20.339+0800][gc,heap     ] GC(8) Eden regions: 232->0(237)
[2025-08-01T15:06:20.339+0800][gc,heap     ] GC(8) Survivor regions: 24->19(32)
[2025-08-01T15:06:20.339+0800][gc,heap     ] GC(8) Old regions: 9->18
[2025-08-01T15:06:20.339+0800][gc,heap     ] GC(8) Humongous regions: 0->0
[2025-08-01T15:06:20.339+0800][gc,metaspace] GC(8) Metaspace: 47185K(47680K)->47185K(47680K) NonClass: 41365K(41664K)->41365K(41664K) Class: 5820K(6016K)->5820K(6016K)
[2025-08-01T15:06:20.339+0800][gc          ] GC(8) Pause Young (Normal) (G1 Evacuation Pause) 263M->35M(512M) 4.458ms
[2025-08-01T15:06:20.339+0800][gc,cpu      ] GC(8) User=0.03s Sys=0.01s Real=0.00s
[2025-08-01T15:06:20.641+0800][gc,start    ] GC(9) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:06:20.641+0800][gc,task     ] GC(9) Using 10 workers of 10 for evacuation
[2025-08-01T15:06:20.646+0800][gc,phases   ] GC(9)   Pre Evacuate Collection Set: 0.19ms
[2025-08-01T15:06:20.646+0800][gc,phases   ] GC(9)   Merge Heap Roots: 0.09ms
[2025-08-01T15:06:20.646+0800][gc,phases   ] GC(9)   Evacuate Collection Set: 4.49ms
[2025-08-01T15:06:20.646+0800][gc,phases   ] GC(9)   Post Evacuate Collection Set: 0.53ms
[2025-08-01T15:06:20.646+0800][gc,phases   ] GC(9)   Other: 0.04ms
[2025-08-01T15:06:20.646+0800][gc,heap     ] GC(9) Eden regions: 237->0(232)
[2025-08-01T15:06:20.646+0800][gc,heap     ] GC(9) Survivor regions: 19->24(32)
[2025-08-01T15:06:20.646+0800][gc,heap     ] GC(9) Old regions: 18->20
[2025-08-01T15:06:20.646+0800][gc,heap     ] GC(9) Humongous regions: 0->0
[2025-08-01T15:06:20.646+0800][gc,metaspace] GC(9) Metaspace: 52975K(53504K)->52975K(53504K) NonClass: 46418K(46720K)->46418K(46720K) Class: 6556K(6784K)->6556K(6784K)
[2025-08-01T15:06:20.646+0800][gc          ] GC(9) Pause Young (Normal) (G1 Evacuation Pause) 272M->42M(512M) 5.453ms
[2025-08-01T15:06:20.646+0800][gc,cpu      ] GC(9) User=0.04s Sys=0.00s Real=0.00s
[2025-08-01T15:06:20.863+0800][gc,start    ] GC(10) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:06:20.863+0800][gc,task     ] GC(10) Using 10 workers of 10 for evacuation
[2025-08-01T15:06:20.867+0800][gc,phases   ] GC(10)   Pre Evacuate Collection Set: 0.11ms
[2025-08-01T15:06:20.867+0800][gc,phases   ] GC(10)   Merge Heap Roots: 0.09ms
[2025-08-01T15:06:20.867+0800][gc,phases   ] GC(10)   Evacuate Collection Set: 3.43ms
[2025-08-01T15:06:20.867+0800][gc,phases   ] GC(10)   Post Evacuate Collection Set: 0.38ms
[2025-08-01T15:06:20.867+0800][gc,phases   ] GC(10)   Other: 0.05ms
[2025-08-01T15:06:20.867+0800][gc,heap     ] GC(10) Eden regions: 232->0(241)
[2025-08-01T15:06:20.867+0800][gc,heap     ] GC(10) Survivor regions: 24->15(32)
[2025-08-01T15:06:20.867+0800][gc,heap     ] GC(10) Old regions: 20->32
[2025-08-01T15:06:20.867+0800][gc,heap     ] GC(10) Humongous regions: 0->0
[2025-08-01T15:06:20.867+0800][gc,metaspace] GC(10) Metaspace: 61003K(61568K)->61003K(61568K) NonClass: 53529K(53824K)->53529K(53824K) Class: 7474K(7744K)->7474K(7744K)
[2025-08-01T15:06:20.867+0800][gc          ] GC(10) Pause Young (Normal) (G1 Evacuation Pause) 274M->45M(512M) 4.165ms
[2025-08-01T15:06:20.867+0800][gc,cpu      ] GC(10) User=0.03s Sys=0.01s Real=0.00s
[2025-08-01T15:06:21.294+0800][gc,start    ] GC(11) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:06:21.298+0800][gc,task     ] GC(11) Using 10 workers of 10 for evacuation
[2025-08-01T15:06:21.301+0800][gc,phases   ] GC(11)   Pre Evacuate Collection Set: 0.11ms
[2025-08-01T15:06:21.303+0800][gc,phases   ] GC(11)   Merge Heap Roots: 0.10ms
[2025-08-01T15:06:21.303+0800][gc,phases   ] GC(11)   Evacuate Collection Set: 2.14ms
[2025-08-01T15:06:21.303+0800][gc,phases   ] GC(11)   Post Evacuate Collection Set: 0.32ms
[2025-08-01T15:06:21.303+0800][gc,phases   ] GC(11)   Other: 0.05ms
[2025-08-01T15:06:21.303+0800][gc,heap     ] GC(11) Eden regions: 241->0(234)
[2025-08-01T15:06:21.303+0800][gc,heap     ] GC(11) Survivor regions: 15->22(32)
[2025-08-01T15:06:21.303+0800][gc,heap     ] GC(11) Old regions: 32->32
[2025-08-01T15:06:21.303+0800][gc,heap     ] GC(11) Humongous regions: 0->0
[2025-08-01T15:06:21.303+0800][gc,metaspace] GC(11) Metaspace: 67048K(67520K)->67048K(67520K) NonClass: 58748K(59008K)->58748K(59008K) Class: 8299K(8512K)->8299K(8512K)
[2025-08-01T15:06:21.303+0800][gc          ] GC(11) Pause Young (Normal) (G1 Evacuation Pause) 286M->52M(512M) 9.499ms
[2025-08-01T15:06:21.303+0800][gc,cpu      ] GC(11) User=0.02s Sys=0.00s Real=0.01s
[2025-08-01T15:06:21.495+0800][gc,start    ] GC(12) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:06:21.495+0800][gc,task     ] GC(12) Using 10 workers of 10 for evacuation
[2025-08-01T15:06:21.499+0800][gc,phases   ] GC(12)   Pre Evacuate Collection Set: 0.17ms
[2025-08-01T15:06:21.499+0800][gc,phases   ] GC(12)   Merge Heap Roots: 0.09ms
[2025-08-01T15:06:21.499+0800][gc,phases   ] GC(12)   Evacuate Collection Set: 2.86ms
[2025-08-01T15:06:21.499+0800][gc,phases   ] GC(12)   Post Evacuate Collection Set: 0.44ms
[2025-08-01T15:06:21.499+0800][gc,phases   ] GC(12)   Other: 0.05ms
[2025-08-01T15:06:21.499+0800][gc,heap     ] GC(12) Eden regions: 234->0(239)
[2025-08-01T15:06:21.499+0800][gc,heap     ] GC(12) Survivor regions: 22->17(32)
[2025-08-01T15:06:21.499+0800][gc,heap     ] GC(12) Old regions: 32->43
[2025-08-01T15:06:21.499+0800][gc,heap     ] GC(12) Humongous regions: 0->0
[2025-08-01T15:06:21.499+0800][gc,metaspace] GC(12) Metaspace: 69236K(69824K)->69236K(69824K) NonClass: 60617K(60928K)->60617K(60928K) Class: 8618K(8896K)->8618K(8896K)
[2025-08-01T15:06:21.499+0800][gc          ] GC(12) Pause Young (Normal) (G1 Evacuation Pause) 286M->58M(512M) 3.709ms
[2025-08-01T15:06:21.499+0800][gc,cpu      ] GC(12) User=0.03s Sys=0.00s Real=0.00s
[2025-08-01T15:06:35.073+0800][gc,start    ] GC(13) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:06:35.073+0800][gc,task     ] GC(13) Using 10 workers of 10 for evacuation
[2025-08-01T15:06:35.078+0800][gc,phases   ] GC(13)   Pre Evacuate Collection Set: 0.12ms
[2025-08-01T15:06:35.078+0800][gc,phases   ] GC(13)   Merge Heap Roots: 0.10ms
[2025-08-01T15:06:35.078+0800][gc,phases   ] GC(13)   Evacuate Collection Set: 3.89ms
[2025-08-01T15:06:35.078+0800][gc,phases   ] GC(13)   Post Evacuate Collection Set: 0.55ms
[2025-08-01T15:06:35.078+0800][gc,phases   ] GC(13)   Other: 0.05ms
[2025-08-01T15:06:35.078+0800][gc,heap     ] GC(13) Eden regions: 239->0(236)
[2025-08-01T15:06:35.078+0800][gc,heap     ] GC(13) Survivor regions: 17->20(32)
[2025-08-01T15:06:35.078+0800][gc,heap     ] GC(13) Old regions: 43->43
[2025-08-01T15:06:35.078+0800][gc,heap     ] GC(13) Humongous regions: 0->0
[2025-08-01T15:06:35.078+0800][gc,metaspace] GC(13) Metaspace: 73532K(74176K)->73532K(74176K) NonClass: 64292K(64640K)->64292K(64640K) Class: 9240K(9536K)->9240K(9536K)
[2025-08-01T15:06:35.078+0800][gc          ] GC(13) Pause Young (Normal) (G1 Evacuation Pause) 297M->61M(512M) 4.908ms
[2025-08-01T15:06:35.078+0800][gc,cpu      ] GC(13) User=0.03s Sys=0.00s Real=0.00s
[2025-08-01T15:20:25.500+0800][gc,start    ] GC(14) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-01T15:20:25.500+0800][gc,task     ] GC(14) Using 10 workers of 10 for evacuation
[2025-08-01T15:20:25.505+0800][gc,phases   ] GC(14)   Pre Evacuate Collection Set: 0.16ms
[2025-08-01T15:20:25.505+0800][gc,phases   ] GC(14)   Merge Heap Roots: 0.11ms
[2025-08-01T15:20:25.505+0800][gc,phases   ] GC(14)   Evacuate Collection Set: 3.62ms
[2025-08-01T15:20:25.505+0800][gc,phases   ] GC(14)   Post Evacuate Collection Set: 0.53ms
[2025-08-01T15:20:25.505+0800][gc,phases   ] GC(14)   Other: 0.05ms
[2025-08-01T15:20:25.505+0800][gc,heap     ] GC(14) Eden regions: 236->0(231)
[2025-08-01T15:20:25.505+0800][gc,heap     ] GC(14) Survivor regions: 20->25(32)
[2025-08-01T15:20:25.505+0800][gc,heap     ] GC(14) Old regions: 43->46
[2025-08-01T15:20:25.505+0800][gc,heap     ] GC(14) Humongous regions: 2->2
[2025-08-01T15:20:25.505+0800][gc,metaspace] GC(14) Metaspace: 81014K(81664K)->81014K(81664K) NonClass: 71072K(71424K)->71072K(71424K) Class: 9941K(10240K)->9941K(10240K)
[2025-08-01T15:20:25.505+0800][gc          ] GC(14) Pause Young (Normal) (G1 Evacuation Pause) 299M->71M(512M) 4.927ms
[2025-08-01T15:20:25.505+0800][gc,cpu      ] GC(14) User=0.03s Sys=0.00s Real=0.00s
[2025-08-01T15:32:41.075+0800][gc,start    ] GC(15) Pause Young (Concurrent Start) (CodeCache GC Threshold)
[2025-08-01T15:32:41.075+0800][gc,task     ] GC(15) Using 10 workers of 10 for evacuation
[2025-08-01T15:32:41.081+0800][gc,phases   ] GC(15)   Pre Evacuate Collection Set: 0.13ms
[2025-08-01T15:32:41.081+0800][gc,phases   ] GC(15)   Merge Heap Roots: 0.09ms
[2025-08-01T15:32:41.081+0800][gc,phases   ] GC(15)   Evacuate Collection Set: 5.30ms
[2025-08-01T15:32:41.081+0800][gc,phases   ] GC(15)   Post Evacuate Collection Set: 0.74ms
[2025-08-01T15:32:41.081+0800][gc,phases   ] GC(15)   Other: 0.06ms
[2025-08-01T15:32:41.081+0800][gc,heap     ] GC(15) Eden regions: 59->0(238)
[2025-08-01T15:32:41.081+0800][gc,heap     ] GC(15) Survivor regions: 25->18(32)
[2025-08-01T15:32:41.081+0800][gc,heap     ] GC(15) Old regions: 46->58
[2025-08-01T15:32:41.081+0800][gc,heap     ] GC(15) Humongous regions: 2->2
[2025-08-01T15:32:41.081+0800][gc,metaspace] GC(15) Metaspace: 82026K(82688K)->82026K(82688K) NonClass: 72040K(72384K)->72040K(72384K) Class: 9985K(10304K)->9985K(10304K)
[2025-08-01T15:32:41.081+0800][gc          ] GC(15) Pause Young (Concurrent Start) (CodeCache GC Threshold) 129M->76M(512M) 6.583ms
[2025-08-01T15:32:41.081+0800][gc          ] GC(16) Concurrent Mark Cycle
[2025-08-01T15:32:41.081+0800][gc,marking  ] GC(16) Concurrent Scan Root Regions
[2025-08-01T15:32:41.082+0800][gc,cpu      ] GC(15) User=0.03s Sys=0.01s Real=0.01s
[2025-08-01T15:32:41.086+0800][gc,marking  ] GC(16) Concurrent Scan Root Regions 4.271ms
[2025-08-01T15:32:41.086+0800][gc,marking  ] GC(16) Concurrent Mark
[2025-08-01T15:32:41.086+0800][gc,marking  ] GC(16) Concurrent Mark From Roots
[2025-08-01T15:32:41.086+0800][gc,task     ] GC(16) Using 3 workers of 3 for marking
[2025-08-01T15:32:41.095+0800][gc,marking  ] GC(16) Concurrent Mark From Roots 9.217ms
[2025-08-01T15:32:41.095+0800][gc,marking  ] GC(16) Concurrent Preclean
[2025-08-01T15:32:41.095+0800][gc,marking  ] GC(16) Concurrent Preclean 0.314ms
[2025-08-01T15:32:41.095+0800][gc,start    ] GC(16) Pause Remark
[2025-08-01T15:32:41.104+0800][gc          ] GC(16) Pause Remark 77M->72M(512M) 8.624ms
[2025-08-01T15:32:41.104+0800][gc,cpu      ] GC(16) User=0.06s Sys=0.00s Real=0.01s
[2025-08-01T15:32:41.104+0800][gc,marking  ] GC(16) Concurrent Mark 18.470ms
[2025-08-01T15:32:41.104+0800][gc,marking  ] GC(16) Concurrent Rebuild Remembered Sets and Scrub Regions
[2025-08-01T15:32:41.110+0800][gc,marking  ] GC(16) Concurrent Rebuild Remembered Sets and Scrub Regions 5.295ms
[2025-08-01T15:32:41.110+0800][gc,start    ] GC(16) Pause Cleanup
[2025-08-01T15:32:41.110+0800][gc          ] GC(16) Pause Cleanup 72M->72M(512M) 0.025ms
[2025-08-01T15:32:41.110+0800][gc,cpu      ] GC(16) User=0.00s Sys=0.00s Real=0.00s
[2025-08-01T15:32:41.110+0800][gc,marking  ] GC(16) Concurrent Clear Claimed Marks
[2025-08-01T15:32:41.110+0800][gc,marking  ] GC(16) Concurrent Clear Claimed Marks 0.052ms
[2025-08-01T15:32:41.110+0800][gc,marking  ] GC(16) Concurrent Cleanup for Next Mark
[2025-08-01T15:32:41.110+0800][gc,marking  ] GC(16) Concurrent Cleanup for Next Mark 0.522ms
[2025-08-01T15:32:41.110+0800][gc          ] GC(16) Concurrent Mark Cycle 28.922ms
[2025-08-01T16:46:42.273+0800][gc,start    ] GC(17) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-08-01T16:46:42.274+0800][gc,task     ] GC(17) Using 10 workers of 10 for evacuation
[2025-08-01T16:46:42.281+0800][gc,phases   ] GC(17)   Pre Evacuate Collection Set: 0.27ms
[2025-08-01T16:46:42.281+0800][gc,phases   ] GC(17)   Merge Heap Roots: 0.23ms
[2025-08-01T16:46:42.281+0800][gc,phases   ] GC(17)   Evacuate Collection Set: 5.35ms
[2025-08-01T16:46:42.281+0800][gc,phases   ] GC(17)   Post Evacuate Collection Set: 1.20ms
[2025-08-01T16:46:42.281+0800][gc,phases   ] GC(17)   Other: 0.10ms
[2025-08-01T16:46:42.281+0800][gc,heap     ] GC(17) Eden regions: 238->0(234)
[2025-08-01T16:46:42.281+0800][gc,heap     ] GC(17) Survivor regions: 18->22(32)
[2025-08-01T16:46:42.281+0800][gc,heap     ] GC(17) Old regions: 53->53
[2025-08-01T16:46:42.281+0800][gc,heap     ] GC(17) Humongous regions: 2->2
[2025-08-01T16:46:42.281+0800][gc,metaspace] GC(17) Metaspace: 82280K(82944K)->82280K(82944K) NonClass: 72325K(72640K)->72325K(72640K) Class: 9954K(10304K)->9954K(10304K)
[2025-08-01T16:46:42.281+0800][gc          ] GC(17) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 309M->75M(512M) 8.236ms
[2025-08-01T16:46:42.281+0800][gc,cpu      ] GC(17) User=0.06s Sys=0.01s Real=0.01s
[2025-08-01T18:01:50.723+0800][gc,start    ] GC(18) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-08-01T18:01:50.725+0800][gc,task     ] GC(18) Using 10 workers of 10 for evacuation
[2025-08-01T18:01:50.740+0800][gc,phases   ] GC(18)   Pre Evacuate Collection Set: 0.54ms
[2025-08-01T18:01:50.740+0800][gc,phases   ] GC(18)   Merge Heap Roots: 0.22ms
[2025-08-01T18:01:50.740+0800][gc,phases   ] GC(18)   Evacuate Collection Set: 12.92ms
[2025-08-01T18:01:50.740+0800][gc,phases   ] GC(18)   Post Evacuate Collection Set: 1.60ms
[2025-08-01T18:01:50.740+0800][gc,phases   ] GC(18)   Other: 0.13ms
[2025-08-01T18:01:50.740+0800][gc,heap     ] GC(18) Eden regions: 234->0(244)
[2025-08-01T18:01:50.740+0800][gc,heap     ] GC(18) Survivor regions: 22->12(32)
[2025-08-01T18:01:50.740+0800][gc,heap     ] GC(18) Old regions: 53->64
[2025-08-01T18:01:50.740+0800][gc,heap     ] GC(18) Humongous regions: 2->2
[2025-08-01T18:01:50.740+0800][gc,metaspace] GC(18) Metaspace: 82386K(83008K)->82386K(83008K) NonClass: 72425K(72704K)->72425K(72704K) Class: 9961K(10304K)->9961K(10304K)
[2025-08-01T18:01:50.740+0800][gc          ] GC(18) Pause Young (Mixed) (G1 Evacuation Pause) 309M->76M(512M) 17.711ms
[2025-08-01T18:01:50.740+0800][gc,cpu      ] GC(18) User=0.06s Sys=0.01s Real=0.02s
[2025-08-01T19:18:50.604+0800][gc,start    ] GC(19) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-08-01T19:18:50.605+0800][gc,task     ] GC(19) Using 10 workers of 10 for evacuation
[2025-08-01T19:18:50.613+0800][gc,phases   ] GC(19)   Pre Evacuate Collection Set: 0.31ms
[2025-08-01T19:18:50.613+0800][gc,phases   ] GC(19)   Merge Heap Roots: 0.15ms
[2025-08-01T19:18:50.613+0800][gc,phases   ] GC(19)   Evacuate Collection Set: 6.35ms
[2025-08-01T19:18:50.613+0800][gc,phases   ] GC(19)   Post Evacuate Collection Set: 1.35ms
[2025-08-01T19:18:50.613+0800][gc,phases   ] GC(19)   Other: 0.11ms
[2025-08-01T19:18:50.613+0800][gc,heap     ] GC(19) Eden regions: 244->0(244)
[2025-08-01T19:18:50.613+0800][gc,heap     ] GC(19) Survivor regions: 12->12(32)
[2025-08-01T19:18:50.613+0800][gc,heap     ] GC(19) Old regions: 64->64
[2025-08-01T19:18:50.613+0800][gc,heap     ] GC(19) Humongous regions: 2->2
[2025-08-01T19:18:50.613+0800][gc,metaspace] GC(19) Metaspace: 82516K(83200K)->82516K(83200K) NonClass: 72548K(72896K)->72548K(72896K) Class: 9968K(10304K)->9968K(10304K)
[2025-08-01T19:18:50.613+0800][gc          ] GC(19) Pause Young (Mixed) (G1 Evacuation Pause) 320M->76M(512M) 9.127ms
[2025-08-01T19:18:50.613+0800][gc,cpu      ] GC(19) User=0.02s Sys=0.00s Real=0.01s
