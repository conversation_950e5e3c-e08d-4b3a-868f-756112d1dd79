[INFO] Scanning for projects...
[INFO] 
[INFO] --------------< com.learningplatform:discussion-service >---------------
[INFO] Building Discussion Service 1.0.0
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.0:run (default-cli) > test-compile @ discussion-service >>>
[WARNING] The artifact mysql:mysql-connector-java:jar:8.0.33 has been relocated to com.mysql:mysql-connector-j:jar:8.0.33: MySQL Connector/J artifacts moved to reverse-DNS compliant Maven 2+ coordinates.
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ discussion-service ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ discussion-service ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ discussion-service ---
[INFO] skip non existing resourceDirectory /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/discussion-service/src/test/resources
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (default-testCompile) @ discussion-service ---
[INFO] No sources to compile
[INFO] 
[INFO] <<< spring-boot:3.2.0:run (default-cli) < test-compile @ discussion-service <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.0:run (default-cli) @ discussion-service ---
[INFO] Attaching agents: []
[2m2025-08-01T18:46:28.900+08:00[0;39m [33m WARN[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
[32m :: Spring Boot :: [39m              [2m (v3.2.0)[0;39m

[2m2025-08-01T18:46:28.919+08:00[0;39m [33m WARN[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-01T18:46:28.922+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.l.d.DiscussionServiceApplication      [0;39m [2m:[0;39m Starting DiscussionServiceApplication using Java 23.0.2 with PID 31989 (/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/discussion-service/target/classes started by jstar in /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/discussion-service)
[2m2025-08-01T18:46:28.922+08:00[0;39m [32mDEBUG[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.l.d.DiscussionServiceApplication      [0;39m [2m:[0;39m Running with Spring Boot v3.2.0, Spring v6.1.1
[2m2025-08-01T18:46:28.922+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.l.d.DiscussionServiceApplication      [0;39m [2m:[0;39m No active profile set, falling back to 1 default profile: "default"
[2m2025-08-01T18:46:29.085+08:00[0;39m [32mDEBUG[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.c.openfeign.FeignClientsRegistrar$1 [0;39m [2m:[0;39m Identified candidate component class: file [/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/discussion-service/target/classes/com/learningplatform/discussion/feign/UserServiceClient.class]
[2m2025-08-01T18:46:29.188+08:00[0;39m [32mDEBUG[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m Generating bean factory id from names: [applicationAvailability, applicationTaskExecutor, basicErrorController, beanNameHandlerMapping, beanNameViewResolver, characterEncodingFilter, com.alibaba.cloud.nacos.NacosServiceAutoConfiguration, com.alibaba.cloud.nacos.discovery.NacosDiscoveryAutoConfiguration, com.alibaba.cloud.nacos.discovery.NacosDiscoveryClientConfiguration, com.alibaba.cloud.nacos.discovery.NacosDiscoveryHeartBeatConfiguration, com.alibaba.cloud.nacos.registry.NacosServiceRegistryAutoConfiguration, com.alibaba.cloud.nacos.util.UtilIPv6AutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration, com.learningplatform.discussion.DiscussionServiceApplication#MapperScannerRegistrar#0, com.learningplatform.discussion.feign.UserServiceClient, compositeCompatibilityVerifier, compositeDiscoveryClient, configDataContextRefresher, configurationPropertiesBeans, configurationPropertiesRebinder, conventionErrorViewResolver, dataInitController, dataSource, dataSourceScriptDatabaseInitializer, ddlApplicationRunner, default.com.learningplatform.discussion.DiscussionServiceApplication.FeignClientSpecification, defaultServletHandlerMapping, defaultValidator, defaultViewResolver, defaultsBindHandlerAdvisor, discussionController, discussionReplyMapper, discussionServiceApplication, discussionServiceImpl, discussionTopicMapper, dispatcherServlet, dispatcherServletRegistration, environmentManager, error, errorAttributes, errorPageCustomizer, errorPageRegistrarBeanPostProcessor, feignChildContextInitializer, feignClientBeanFactoryInitializationCodeGenerator, feignContext, feignFeature, feignTargeter, fileWatcher, flashMapManager, formContentFilter, handlerExceptionResolver, handlerFunctionAdapter, healthController, hikariPoolDataSourceMetadataProvider, httpMessageConvertersRestClientCustomizer, httpRequestHandlerAdapter, inetIPv6Utils, inetUtils, inetUtilsProperties, jacksonObjectMapper, jacksonObjectMapperBuilder, jdbcClient, jdbcConnectionDetails, jdbcConnectionDetailsHikariBeanPostProcessor, jdbcTemplate, jsonComponentModule, jsonMixinModule, jsonMixinModuleEntries, lifecycleProcessor, loadBalancerClientsDefaultsMappingsProvider, localeCharsetMappingsCustomizer, localeResolver, loggingRebinder, mappingJackson2HttpMessageConverter, messageConverters, methodValidationPostProcessor, multipartConfigElement, multipartResolver, mvcContentNegotiationManager, mvcConversionService, mvcHandlerMappingIntrospector, mvcPathMatcher, mvcPatternParser, mvcResourceUrlProvider, mvcUriComponentsContributor, mvcUrlPathHelper, mvcValidator, mvcViewResolver, myBatisPlusConfig, mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties, nacosAutoServiceRegistration, nacosDiscoveryClient, nacosProperties, nacosRegistration, nacosServiceDiscovery, nacosServiceManager, nacosServiceRegistry, namedParameterJdbcTemplate, org.springframework.aop.config.internalAutoProxyCreator, org.springframework.boot.autoconfigure.AutoConfigurationPackages, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration, org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration, org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration, org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration, org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration, org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonMixinConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari, org.springframework.boot.autoconfigure.jdbc.DataSourceJmxConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceJmxConfiguration$Hikari, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcClientAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration, org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration, org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration, org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$TaskExecutorBuilderConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$TaskExecutorConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$ThreadPoolTaskExecutorBuilderConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$TaskSchedulerBuilderConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$ThreadPoolTaskSchedulerBuilderConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizationAutoConfiguration, org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration, org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration, org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration, org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration, org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration, org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration, org.springframework.boot.context.internalConfigurationPropertiesBinder, org.springframework.boot.context.properties.BoundConfigurationProperties, org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor, org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter, org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor, org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration, org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshScopeBeanDefinitionEnhancer, org.springframework.cloud.client.CommonsClientAutoConfiguration, org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration, org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration, org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration, org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationConfiguration, org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration, org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration, org.springframework.cloud.commons.security.ResourceServerTokenRelayAutoConfiguration, org.springframework.cloud.commons.security.ResourceServerTokenRelayAutoConfiguration$ResourceServerTokenRelayRegistrationAutoConfiguration, org.springframework.cloud.commons.util.UtilAutoConfiguration, org.springframework.cloud.configuration.CompatibilityVerifierAutoConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration$DefaultFeignTargeterConfiguration, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.event.internalEventListenerFactory, org.springframework.context.event.internalEventListenerProcessor, org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration, org.springframework.transaction.config.internalTransactionAdvisor, org.springframework.transaction.config.internalTransactionalEventListenerFactory, parameterNamesModule, persistenceExceptionTranslationPostProcessor, platformTransactionManagerCustomizers, preserveErrorControllerTargetClassPostProcessor, propertySourcesPlaceholderConfigurer, refreshEventListener, refreshScope, refreshScopeLifecycle, replyLikeMapper, requestContextFilter, requestMappingHandlerAdapter, requestMappingHandlerMapping, resourceHandlerMapping, restClientBuilder, restClientBuilderConfigurer, restClientSsl, restTemplateBuilder, restTemplateBuilderConfigurer, routerFunctionMapping, server-org.springframework.boot.autoconfigure.web.ServerProperties, servletWebServerFactoryCustomizer, simpleAsyncTaskExecutorBuilder, simpleAsyncTaskSchedulerBuilder, simpleControllerHandlerAdapter, simpleDiscoveryClient, simpleDiscoveryProperties, sleuthPresentVerifier, spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties, spring.cloud.openfeign.client-org.springframework.cloud.openfeign.FeignClientProperties, spring.cloud.openfeign.encoder-org.springframework.cloud.openfeign.support.FeignEncoderProperties, spring.cloud.openfeign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties, spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties, spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties, spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties, spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties, spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties, spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties, spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties, spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties, spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties, spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties, spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties, spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties, spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties, spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties, spring.web-org.springframework.boot.autoconfigure.web.WebProperties, springBootVersionVerifier, sqlSessionFactory, sqlSessionTemplate, sslBundleRegistry, sslPropertiesSslBundleRegistrar, standardJacksonObjectMapperBuilderCustomizer, stringHttpMessageConverter, taskExecutorBuilder, taskSchedulerBuilder, testDataController, themeResolver, threadPoolTaskExecutorBuilder, threadPoolTaskSchedulerBuilder, tomcatServletWebServerFactory, tomcatServletWebServerFactoryCustomizer, tomcatWebServerFactoryCustomizer, transactionAttributeSource, transactionExecutionListeners, transactionInterceptor, transactionManager, transactionTemplate, user-service.FeignClientSpecification, viewControllerHandlerMapping, viewNameTranslator, viewResolver, webServerFactoryCustomizerBeanPostProcessor, websocketServletWebServerCustomizer, welcomePageHandlerMapping, welcomePageNotAcceptableHandlerMapping]
[2m2025-08-01T18:46:29.189+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=1a34dac5-e725-3a71-b711-08d58ddd5936
[2m2025-08-01T18:46:29.298+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8085 (http)
[2m2025-08-01T18:46:29.301+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-01T18:46:29.301+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.16]
[2m2025-08-01T18:46:29.320+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-01T18:46:29.320+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 375 ms
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
Initialization Sequence datacenterId:0 workerId:12
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.5.5 
[2m2025-08-01T18:46:29.457+08:00[0;39m [32mDEBUG[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.c.openfeign.FeignClientFactoryBean  [0;39m [2m:[0;39m Creating a FeignClientFactoryBean.
[2m2025-08-01T18:46:29.623+08:00[0;39m [32mDEBUG[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.c.c.SpringBootVersionVerifier       [0;39m [2m:[0;39m Version found in Boot manifest [3.2.0]
[2m2025-08-01T18:46:29.624+08:00[0;39m [32mDEBUG[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.c.c.CompositeCompatibilityVerifier  [0;39m [2m:[0;39m All conditions are passing
[2m2025-08-01T18:46:29.638+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8085 (http) with context path ''
[2m2025-08-01T18:46:29.640+08:00[0;39m [33m WARN[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-01T18:46:29.640+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ans.namespace attribute : null
[2m2025-08-01T18:46:29.641+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[2m2025-08-01T18:46:29.641+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from namespace attribute :null
[2m2025-08-01T18:46:29.647+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [req-serv] nacos-server port:8848
[2m2025-08-01T18:46:29.647+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [http-client] connect timeout:1000
[2m2025-08-01T18:46:29.647+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m PER_TASK_CONFIG_SIZE: 3000.0
[2m2025-08-01T18:46:29.648+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[2m2025-08-01T18:46:29.648+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[2m2025-08-01T18:46:29.662+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialWatcher  [0;39m [2m:[0;39m null No credential found
[2m2025-08-01T18:46:29.665+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [RpcClientFactory] create a new rpc client of ec38e9c4-afea-42bc-8f4b-dbb6b4de5f48
[2m2025-08-01T18:46:29.671+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [ec38e9c4-afea-42bc-8f4b-dbb6b4de5f48] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[2m2025-08-01T18:46:29.671+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [ec38e9c4-afea-42bc-8f4b-dbb6b4de5f48] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[2m2025-08-01T18:46:29.671+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [ec38e9c4-afea-42bc-8f4b-dbb6b4de5f48] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[2m2025-08-01T18:46:29.671+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [ec38e9c4-afea-42bc-8f4b-dbb6b4de5f48] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
[2m2025-08-01T18:46:29.681+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"OPENSSL","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
[2m2025-08-01T18:46:29.841+08:00[0;39m [31mERROR[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [tor-localhost-8][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m [1754045189810_127.0.0.1_54715]Error to process server push response: {"headers":{},"abilityTable":{"supportPersistentInstanceByGrpc":true},"module":"internal"}
[2m2025-08-01T18:46:29.936+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [ec38e9c4-afea-42bc-8f4b-dbb6b4de5f48] Success to connect to server [localhost:8848] on start up, connectionId = 1754045189810_127.0.0.1_54715
[2m2025-08-01T18:46:29.937+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [ec38e9c4-afea-42bc-8f4b-dbb6b4de5f48] Notify connected event to listeners.
[2m2025-08-01T18:46:29.937+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [ec38e9c4-afea-42bc-8f4b-dbb6b4de5f48] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[2m2025-08-01T18:46:29.937+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m Grpc connection connect
[2m2025-08-01T18:46:29.937+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [ec38e9c4-afea-42bc-8f4b-dbb6b4de5f48] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda/0x00000300016ddbe0
[2m2025-08-01T18:46:29.937+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [REGISTER-SERVICE] public registering service discussion-service with instance Instance{instanceId='null', ip='*************', port=8085, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[2m2025-08-01T18:46:29.944+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m nacos registry, DEFAULT_GROUP discussion-service *************:8085 register finished
[2m2025-08-01T18:46:29.949+08:00[0;39m [32m INFO[0;39m [35m31989[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.l.d.DiscussionServiceApplication      [0;39m [2m:[0;39m Started DiscussionServiceApplication in 1.177 seconds (process running for 1.306)
