server:
  port: 8090

spring:
  application:
    name: gateway-service
  main:
    web-application-type: reactive
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: public
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        # 用户服务路由
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/api/user/**
        
        # 课程服务路由
        - id: course-service
          uri: lb://course-service
          predicates:
            - Path=/api/course/**
        
        # 学习服务路由
        - id: learning-service
          uri: lb://learning-service
          predicates:
            - Path=/api/learning/**
        
        # 推荐服务路由
        - id: recommendation-service
          uri: lb://recommendation-service
          predicates:
            - Path=/api/recommendation/**
        
        # 讨论服务路由
        - id: discussion-service
          uri: lb://discussion-service
          predicates:
            - Path=/api/discussion/**
      
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origin-patterns: "*"
            allowed-methods: "*"
            allowed-headers: "*"
            allow-credentials: true

# JWT配置
jwt:
  secret: mySecretKey123456789012345678901234567890
  expiration: 86400000  # 24小时

management:
  endpoints:
    web:
      exposure:
        include: health,info,gateway
  endpoint:
    gateway:
      enabled: true

logging:
  level:
    org.springframework.cloud.gateway: DEBUG
    com.learningplatform.gateway: DEBUG